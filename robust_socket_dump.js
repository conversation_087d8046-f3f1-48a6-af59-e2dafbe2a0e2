// robust_socket_dump.js - Multiple approaches to hook network functions
console.log("[*] Starting robust socket dump...");

// Method 1: Try different libraries for recv/send
const libraries = [
    null,           // Default search
    "libc.so.6",    // Standard C library
    "/lib/x86_64-linux-gnu/libc.so.6",  // Full path
    "FTNN"          // Main binary
];

const functions = ['recv', 'send', 'read', 'write'];

let hooksInstalled = 0;

libraries.forEach(lib => {
    functions.forEach(func => {
        try {
            const funcPtr = Module.getExportByName(lib, func);
            if (funcPtr) {
                console.log(`[+] Found ${func} in ${lib || 'default'}`);
                
                if (func === 'recv' || func === 'read') {
                    Interceptor.attach(funcPtr, {
                        onEnter: function(args) {
                            this.fd = args[0].toInt32();
                            this.buf = args[1];
                            this.len = args[2].toInt32();
                            this.func = func;
                        },
                        onLeave: function(retval) {
                            const bytes = retval.toInt32();
                            if (bytes > 0 && this.fd > 2) { // Skip stdin/stdout/stderr
                                console.log(`\n[<-- ${this.func.toUpperCase()} fd ${this.fd} | ${bytes} bytes]`);
                                try {
                                    console.log(hexdump(this.buf, {
                                        length: Math.min(bytes, 128),
                                        header: true,
                                        ansi: true
                                    }));
                                } catch (e) {
                                    console.log("[-] Could not dump buffer: " + e);
                                }
                            }
                        }
                    });
                    hooksInstalled++;
                } else if (func === 'send' || func === 'write') {
                    Interceptor.attach(funcPtr, {
                        onEnter: function(args) {
                            this.fd = args[0].toInt32();
                            this.buf = args[1];
                            this.len = args[2].toInt32();
                            this.func = func;
                            
                            if (this.fd > 2 && this.len > 0) { // Skip stdin/stdout/stderr
                                console.log(`\n[--> ${this.func.toUpperCase()} fd ${this.fd} | ${this.len} bytes]`);
                                try {
                                    console.log(hexdump(this.buf, {
                                        length: Math.min(this.len, 128),
                                        header: true,
                                        ansi: true
                                    }));
                                } catch (e) {
                                    console.log("[-] Could not dump buffer: " + e);
                                }
                            }
                        }
                    });
                    hooksInstalled++;
                }
            }
        } catch (e) {
            // Silently continue if function not found
        }
    });
});

// Method 2: Hook connect() to see when connections are made
try {
    const connectPtr = Module.getExportByName(null, 'connect');
    if (connectPtr) {
        console.log("[+] Found connect function");
        Interceptor.attach(connectPtr, {
            onEnter: function(args) {
                const sockfd = args[0].toInt32();
                const addr = args[1];
                console.log(`\n[*] CONNECT attempt on socket ${sockfd}`);
                
                try {
                    // Try to read sockaddr structure
                    const family = addr.readU16();
                    if (family === 2) { // AF_INET
                        const port = ((addr.add(2).readU8() << 8) | addr.add(3).readU8());
                        const ip = `${addr.add(4).readU8()}.${addr.add(5).readU8()}.${addr.add(6).readU8()}.${addr.add(7).readU8()}`;
                        console.log(`[*] Connecting to ${ip}:${port}`);
                    }
                } catch (e) {
                    console.log("[*] Could not parse address");
                }
            },
            onLeave: function(retval) {
                const result = retval.toInt32();
                console.log(`[*] Connect result: ${result}`);
            }
        });
        hooksInstalled++;
    }
} catch (e) {
    console.log("[-] Could not hook connect: " + e);
}

// Method 3: Hook socket() to see when sockets are created
try {
    const socketPtr = Module.getExportByName(null, 'socket');
    if (socketPtr) {
        console.log("[+] Found socket function");
        Interceptor.attach(socketPtr, {
            onEnter: function(args) {
                const domain = args[0].toInt32();
                const type = args[1].toInt32();
                const protocol = args[2].toInt32();
                console.log(`\n[*] SOCKET created: domain=${domain}, type=${type}, protocol=${protocol}`);
            },
            onLeave: function(retval) {
                const sockfd = retval.toInt32();
                console.log(`[*] Socket FD: ${sockfd}`);
            }
        });
        hooksInstalled++;
    }
} catch (e) {
    console.log("[-] Could not hook socket: " + e);
}

console.log(`[*] Installed ${hooksInstalled} hooks. Monitoring network activity...`);

if (hooksInstalled === 0) {
    console.log("[-] WARNING: No hooks installed! Network monitoring may not work.");
    console.log("[*] Trying alternative approach...");
    
    // Method 4: Scan for network activity using system calls
    const syscalls = ['sendto', 'recvfrom', 'sendmsg', 'recvmsg'];
    syscalls.forEach(syscall => {
        try {
            const ptr = Module.getExportByName(null, syscall);
            if (ptr) {
                console.log(`[+] Found ${syscall}`);
                Interceptor.attach(ptr, {
                    onEnter: function(args) {
                        console.log(`[*] ${syscall} called`);
                    }
                });
            }
        } catch (e) {}
    });
}
