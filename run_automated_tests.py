#!/usr/bin/env python3
"""
Automated Test Runner - Executes non-interactive tests and prepares for interactive ones
"""

import subprocess
import os
import json
from datetime import datetime
from pathlib import Path

class AutomatedTestRunner:
    def __init__(self):
        self.test_data_dir = Path("test_data")
        self.test_data_dir.mkdir(exist_ok=True)
        self.results = {}
        
    def log_result(self, test_name, status, details=""):
        """Log test result"""
        self.results[test_name] = {
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'details': details
        }
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status}")
        if details:
            print(f"   📋 {details}")
    
    def test_system_requirements(self):
        """Test if required tools are available"""
        print("🔧 TESTING SYSTEM REQUIREMENTS")
        print("-" * 40)
        
        required_tools = [
            ('python3', 'Python 3 interpreter'),
            ('tcpdump', 'Network packet capture'),
            ('pgrep', 'Process grep tool')
        ]

        optional_tools = [
            ('tshark', 'Wireshark command-line tool (preferred for analysis)'),
            ('netstat', 'Network statistics tool'),
            ('ss', 'Socket statistics tool (netstat alternative)'),
            ('lsof', 'List open files tool (network connection alternative)')
        ]
        
        all_good = True

        # Check required tools
        for tool, description in required_tools:
            try:
                result = subprocess.run(['which', tool], capture_output=True, text=True)
                if result.returncode == 0:
                    self.log_result(f"required_{tool}", "PASS", f"Found at {result.stdout.strip()}")
                else:
                    self.log_result(f"required_{tool}", "FAIL", f"Not found - {description}")
                    all_good = False
            except Exception as e:
                self.log_result(f"required_{tool}", "FAIL", f"Error checking: {e}")
                all_good = False

        # Check optional tools (don't affect all_good status)
        for tool, description in optional_tools:
            try:
                result = subprocess.run(['which', tool], capture_output=True, text=True)
                if result.returncode == 0:
                    self.log_result(f"optional_{tool}", "PASS", f"Found at {result.stdout.strip()}")
                else:
                    self.log_result(f"optional_{tool}", "WARN", f"Not found - {description}")
            except Exception as e:
                self.log_result(f"optional_{tool}", "WARN", f"Error checking: {e}")

        return all_good
    
    def test_ftnn_installation(self):
        """Test FTNN installation"""
        print("\n🏢 TESTING FTNN INSTALLATION")
        print("-" * 40)
        
        ftnn_path = Path("/opt/FTNN/FTNN")
        if ftnn_path.exists():
            self.log_result("ftnn_installation", "PASS", f"Found at {ftnn_path}")
            
            # Check if executable
            if os.access(ftnn_path, os.X_OK):
                self.log_result("ftnn_executable", "PASS", "FTNN is executable")
            else:
                self.log_result("ftnn_executable", "FAIL", "FTNN is not executable")
                
            return True
        else:
            self.log_result("ftnn_installation", "FAIL", "FTNN not found at /opt/FTNN/FTNN")
            return False
    
    def test_ftnn_status(self):
        """Check if FTNN is running"""
        print("\n🔍 TESTING FTNN STATUS")
        print("-" * 40)
        
        try:
            # Check if FTNN process is running
            result = subprocess.run(['pgrep', '-f', 'FTNN'], capture_output=True, text=True)
            if result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                self.log_result("ftnn_running", "PASS", f"FTNN running with PIDs: {', '.join(pids)}")
                
                # Check network connections using available tools
                connections = []

                # Try ss first (modern netstat replacement)
                try:
                    ss_result = subprocess.run(['ss', '-tnp'], capture_output=True, text=True)
                    for line in ss_result.stdout.split('\n'):
                        if 'FTNN' in line and ('ESTABLISHED' in line or 'ESTAB' in line):
                            connections.append(line.strip())
                except FileNotFoundError:
                    # Try netstat
                    try:
                        netstat_result = subprocess.run(['netstat', '-tnp'], capture_output=True, text=True)
                        for line in netstat_result.stdout.split('\n'):
                            if 'FTNN' in line and 'ESTABLISHED' in line:
                                connections.append(line.strip())
                    except FileNotFoundError:
                        # Try lsof as last resort
                        try:
                            lsof_result = subprocess.run(['lsof', '-i', '-n'], capture_output=True, text=True)
                            for line in lsof_result.stdout.split('\n'):
                                if 'FTNN' in line and ('ESTABLISHED' in line or 'TCP' in line):
                                    connections.append(line.strip())
                        except FileNotFoundError:
                            self.log_result("ftnn_connections", "WARN", "No network tools available to check connections")
                            return False
                
                if connections:
                    self.log_result("ftnn_connections", "PASS", f"Found {len(connections)} network connections")
                    for i, conn in enumerate(connections):
                        print(f"   Connection {i+1}: {conn}")
                    return True
                else:
                    self.log_result("ftnn_connections", "WARN", "FTNN running but no network connections found")
                    print("   💡 You may need to login to FTNN to establish server connections")
                    return False
            else:
                self.log_result("ftnn_running", "FAIL", "FTNN is not running")
                print("   💡 Start FTNN with: /opt/FTNN/FTNN")
                return False
                
        except Exception as e:
            self.log_result("ftnn_status_check", "FAIL", f"Error checking FTNN status: {e}")
            return False
    
    def test_parser_functionality(self):
        """Test our protocol parser with existing data"""
        print("\n🔬 TESTING PROTOCOL PARSER")
        print("-" * 40)
        
        # Check if parser files exist
        parser_files = ['futu_protocol_parser.py', 'analyze_payloads.py', 'session_replay.py']
        all_exist = True
        
        for filename in parser_files:
            if Path(filename).exists():
                self.log_result(f"parser_file_{filename}", "PASS", f"File exists")
            else:
                self.log_result(f"parser_file_{filename}", "FAIL", f"File missing")
                all_exist = False
        
        if not all_exist:
            return False
        
        # Test parser import
        try:
            from futu_protocol_parser import FutuProtocolParser
            parser = FutuProtocolParser()
            self.log_result("parser_import", "PASS", "Parser imported successfully")
        except Exception as e:
            self.log_result("parser_import", "FAIL", f"Import failed: {e}")
            return False
        
        # Test payload analyzer import
        try:
            from analyze_payloads import PayloadAnalyzer
            analyzer = PayloadAnalyzer()
            self.log_result("analyzer_import", "PASS", "Payload analyzer imported successfully")
        except Exception as e:
            self.log_result("analyzer_import", "FAIL", f"Import failed: {e}")
            return False
        
        return True
    
    def check_existing_data(self):
        """Check for any existing capture data"""
        print("\n📁 CHECKING EXISTING DATA")
        print("-" * 40)
        
        # Look for pcap files
        pcap_files = list(Path(".").glob("*.pcap")) + list(self.test_data_dir.glob("*.pcap"))
        if pcap_files:
            self.log_result("existing_pcap", "PASS", f"Found {len(pcap_files)} pcap files")
            for pcap in pcap_files:
                size = pcap.stat().st_size
                print(f"   📦 {pcap}: {size} bytes")
        else:
            self.log_result("existing_pcap", "WARN", "No existing pcap files found")
        
        # Look for log files
        log_files = list(Path(".").glob("*.log")) + list(self.test_data_dir.glob("*.log"))
        if log_files:
            self.log_result("existing_logs", "PASS", f"Found {len(log_files)} log files")
            for log in log_files:
                size = log.stat().st_size
                print(f"   📄 {log}: {size} bytes")
        else:
            self.log_result("existing_logs", "WARN", "No existing log files found")
        
        # Look for JSON results
        json_files = list(Path(".").glob("*.json")) + list(self.test_data_dir.glob("*.json"))
        if json_files:
            self.log_result("existing_json", "PASS", f"Found {len(json_files)} JSON files")
            for json_file in json_files:
                size = json_file.stat().st_size
                print(f"   📊 {json_file}: {size} bytes")
        else:
            self.log_result("existing_json", "WARN", "No existing JSON files found")
    
    def generate_readiness_report(self):
        """Generate a report on system readiness"""
        print("\n📊 SYSTEM READINESS REPORT")
        print("=" * 60)
        
        # Count results
        passed = sum(1 for r in self.results.values() if r['status'] == 'PASS')
        failed = sum(1 for r in self.results.values() if r['status'] == 'FAIL')
        warnings = sum(1 for r in self.results.values() if r['status'] == 'WARN')
        
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"⚠️  Warnings: {warnings}")
        
        # Determine readiness level
        critical_failures = [
            name for name, result in self.results.items() 
            if result['status'] == 'FAIL' and any(x in name for x in ['tool_', 'ftnn_installation', 'parser_'])
        ]
        
        if critical_failures:
            print(f"\n❌ SYSTEM NOT READY")
            print(f"Critical failures:")
            for failure in critical_failures:
                print(f"   - {failure}: {self.results[failure]['details']}")
        else:
            ftnn_ready = self.results.get('ftnn_connections', {}).get('status') == 'PASS'
            if ftnn_ready:
                print(f"\n✅ SYSTEM FULLY READY")
                print(f"✅ Ready for interactive protocol testing")
            else:
                print(f"\n⚠️  SYSTEM PARTIALLY READY")
                print(f"⚠️  FTNN needs to be logged in for full testing")
        
        # Save detailed report
        report_file = self.test_data_dir / "readiness_report.json"
        with open(report_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'passed': passed,
                    'failed': failed,
                    'warnings': warnings,
                    'ready_for_testing': len(critical_failures) == 0
                },
                'detailed_results': self.results
            }, f, indent=2)
        
        print(f"\n📋 Detailed report saved to: {report_file}")
        
        return len(critical_failures) == 0
    
    def show_next_steps(self):
        """Show recommended next steps"""
        print(f"\n🎯 RECOMMENDED NEXT STEPS")
        print("-" * 40)
        
        ftnn_ready = self.results.get('ftnn_connections', {}).get('status') == 'PASS'
        
        if ftnn_ready:
            print("✅ System is ready for protocol testing!")
            print()
            print("Run interactive tests:")
            print("   python3 automated_protocol_tester.py")
            print()
            print("Or run specific capture scenarios:")
            print("   1. Login sequence capture")
            print("   2. Quote subscription capture") 
            print("   3. Option chain capture")
            print("   4. Heartbeat/idle capture")
        else:
            print("⚠️  Complete setup first:")
            print()
            if self.results.get('ftnn_running', {}).get('status') != 'PASS':
                print("1. Start FTNN:")
                print("   /opt/FTNN/FTNN")
                print()
            
            if self.results.get('ftnn_connections', {}).get('status') != 'PASS':
                print("2. Login to FTNN to establish server connection")
                print()
            
            print("3. Then run the interactive tester:")
            print("   python3 automated_protocol_tester.py")

def main():
    print("🧪 FUTU PROTOCOL AUTOMATED TEST RUNNER")
    print("=" * 60)
    print("Running system readiness checks...")
    print()
    
    runner = AutomatedTestRunner()
    
    # Run all automated tests
    tools_ok = runner.test_system_requirements()
    ftnn_ok = runner.test_ftnn_installation()
    status_ok = runner.test_ftnn_status()
    parser_ok = runner.test_parser_functionality()
    runner.check_existing_data()
    
    # Generate report
    system_ready = runner.generate_readiness_report()
    runner.show_next_steps()

if __name__ == "__main__":
    main()
