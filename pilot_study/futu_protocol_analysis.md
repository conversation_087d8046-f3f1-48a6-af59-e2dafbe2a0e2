# Futu Protocol Analysis & Reverse Engineering Guide

## 🎯 Executive Summary

**BREAKTHROUGH ACHIEVED**: Futu FTNN client uses a **custom binary protocol** called "FT'n-X", NOT encrypted TLS. This makes reverse engineering significantly easier than initially expected.

## 📊 Protocol Discovery Results

### Key Findings
- ✅ **Protocol Type**: Custom binary (not TLS/SSL)
- ✅ **Protocol Name**: "FT'n-X" (Futu-X)
- ✅ **Encryption**: None detected (plaintext binary)
- ✅ **Capture Method**: System-level `strace` monitoring
- ✅ **Traffic Volume**: High-frequency real-time data

### Technical Details
- **Header Signature**: `"FT'n-X\0"` (7 bytes)
- **Message Sizes**: 83-87 bytes typical
- **File Descriptors**: FD 100 and 129 (dual connections)
- **Transport**: TCP sockets with `recvmsg()`/`sendmsg()`
- **Data Format**: Binary with length prefixes

## 🔍 Captured Traffic Analysis

### Sample Message Structure
```
Hex: 46 54 27 6E 2D 58 00 0D E2 2E 75 01 00 00 00 00 18 44 00 00 00 33 00 00 00 00 00 00 00 00 00 00
ASCII: "FT'n-X\0\r\342.u\1\0\0\0\0\30D\0\0\0003\0\0\0\0\0\0\0\0\0\0"
```

### Protocol Structure Analysis
```
Offset | Length | Description
-------|--------|-------------
0x00   | 7      | Header: "FT'n-X\0"
0x07   | 1      | Control byte (0x0D)
0x08   | 4      | Possible message ID/sequence
0x0C   | 4      | Length field (little-endian)
0x10   | 4      | Message type/command
0x14   | 4      | Data length
0x18   | N      | Payload data
```

## 🛠️ Actionable Next Steps

### Phase 1: Protocol Decoding (Immediate - 1-2 hours)

#### Step 1: Enhanced Traffic Capture
```bash
# Capture all network traffic with full message content
sudo strace -p $(pidof FTNN) -e trace=sendmsg,recvmsg -f -s 1000 -o futu_full_traffic.log &

# Let it run for 5-10 minutes to capture various message types
sleep 600 && sudo pkill strace

# Analyze the captured data
grep "FT'n-X" futu_full_traffic.log | head -20
```

#### Step 2: Message Type Classification
```bash
# Extract unique message patterns
grep -o "FT'n-X[^\"]*" futu_full_traffic.log | sort | uniq -c | sort -nr > message_patterns.txt

# Identify message types by length and content
awk '/recvmsg.*FT.n-X/ {print length($0), $0}' futu_full_traffic.log | sort -n > messages_by_length.txt
```

#### Step 3: Create Protocol Parser
```python
# futu_protocol_parser.py
import struct
import re

def parse_futu_message(data):
    if not data.startswith(b"FT'n-X\0"):
        return None
    
    header = data[:7]
    control = data[7]
    msg_id = struct.unpack('<I', data[8:12])[0]
    length = struct.unpack('<I', data[12:16])[0]
    msg_type = struct.unpack('<I', data[16:20])[0]
    data_len = struct.unpack('<I', data[20:24])[0]
    payload = data[24:24+data_len]
    
    return {
        'header': header,
        'control': control,
        'msg_id': msg_id,
        'length': length,
        'msg_type': msg_type,
        'data_len': data_len,
        'payload': payload
    }

# Usage: python3 futu_protocol_parser.py futu_full_traffic.log
```

### Phase 2: Real-Time Interception (2-4 hours)

#### Step 4: Create Traffic Interceptor
Since Frida hooks failed, use alternative approaches:

**Option A: LD_PRELOAD Hook**
```c
// futu_intercept.c - Compile with: gcc -shared -fPIC futu_intercept.c -o futu_intercept.so
#include <sys/socket.h>
#include <dlfcn.h>
#include <stdio.h>

static ssize_t (*real_recvmsg)(int sockfd, struct msghdr *msg, int flags) = NULL;

ssize_t recvmsg(int sockfd, struct msghdr *msg, int flags) {
    if (!real_recvmsg) real_recvmsg = dlsym(RTLD_NEXT, "recvmsg");
    
    ssize_t result = real_recvmsg(sockfd, msg, flags);
    
    if (result > 0 && msg->msg_iov && msg->msg_iov[0].iov_base) {
        char *data = (char*)msg->msg_iov[0].iov_base;
        if (strncmp(data, "FT'n-X", 6) == 0) {
            printf("[INTERCEPT] FD %d: %zd bytes\n", sockfd, result);
            // Log or modify data here
        }
    }
    
    return result;
}

// Usage: LD_PRELOAD=./futu_intercept.so /opt/FTNN/FTNN
```

**Option B: Network Proxy**
```python
# futu_proxy.py - TCP proxy to intercept and analyze traffic
import socket
import threading
import struct

def parse_and_log(data, direction):
    if data.startswith(b"FT'n-X"):
        print(f"[{direction}] Futu message: {len(data)} bytes")
        # Parse and log message details
        
def proxy_connection(client_socket, target_host, target_port):
    target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    target_socket.connect((target_host, target_port))
    
    def forward_data(source, destination, direction):
        while True:
            data = source.recv(4096)
            if not data: break
            parse_and_log(data, direction)
            destination.send(data)
    
    threading.Thread(target=forward_data, args=(client_socket, target_socket, "C->S")).start()
    threading.Thread(target=forward_data, args=(target_socket, client_socket, "S->C")).start()

# Run proxy and redirect FTNN traffic through it
```

### Phase 3: API Discovery (4-8 hours)

#### Step 5: Message Correlation
```bash
# Correlate GUI actions with network messages
# 1. Start traffic capture
# 2. Perform specific actions in FTNN GUI
# 3. Analyze which messages correspond to which actions

# Example workflow:
echo "Starting login capture..."
sudo strace -p $(pidof FTNN) -e trace=sendmsg,recvmsg -f -s 1000 -o login_traffic.log &
# Perform login in GUI
sleep 30 && sudo pkill strace

echo "Starting market data capture..."
sudo strace -p $(pidof FTNN) -e trace=sendmsg,recvmsg -f -s 1000 -o market_data_traffic.log &
# Navigate to market data in GUI
sleep 30 && sudo pkill strace
```

#### Step 6: API Endpoint Mapping
```python
# api_mapper.py - Map message types to API functions
message_types = {
    0x1001: "login_request",
    0x1002: "login_response", 
    0x2001: "market_data_request",
    0x2002: "market_data_response",
    0x3001: "order_request",
    0x3002: "order_response"
}

def analyze_message_flow(log_file):
    # Parse log and identify request/response patterns
    # Build API documentation automatically
    pass
```

### Phase 4: Protocol Implementation (8-16 hours)

#### Step 7: Create Futu API Client
```python
# futu_api_client.py - Custom implementation of Futu protocol
import socket
import struct

class FutuClient:
    def __init__(self, host, port):
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socket.connect((host, port))
        self.msg_id = 1
    
    def send_message(self, msg_type, payload=b""):
        header = b"FT'n-X\0"
        control = 0x0D
        length = 24 + len(payload)
        
        message = struct.pack('<7sBIIII', 
            header, control, self.msg_id, length, 
            msg_type, len(payload)) + payload
        
        self.socket.send(message)
        self.msg_id += 1
        
        return self.receive_message()
    
    def receive_message(self):
        # Implement message parsing
        pass
    
    def login(self, username, password):
        # Implement login protocol
        pass
    
    def get_market_data(self, symbol):
        # Implement market data request
        pass

# Usage:
# client = FutuClient("server_ip", server_port)
# client.login("username", "password")
# data = client.get_market_data("AAPL")
```

## 🚀 Success Probability & Timeline

### High Probability Tasks (90%+ success)
- ✅ **Protocol Structure Decoding** (2-4 hours)
- ✅ **Message Type Identification** (4-8 hours)
- ✅ **Basic API Mapping** (8-16 hours)

### Medium Probability Tasks (70%+ success)
- ⚠️ **Authentication Bypass** (depends on complexity)
- ⚠️ **Real-time Data Streaming** (may require session management)
- ⚠️ **Order Placement** (may have additional security)

### Timeline Estimate
- **Phase 1**: 1-2 hours (protocol decoding)
- **Phase 2**: 2-4 hours (interception setup)
- **Phase 3**: 4-8 hours (API discovery)
- **Phase 4**: 8-16 hours (implementation)
- **Total**: 15-30 hours for complete reverse engineering

## 🎯 Immediate Action Items

1. **START NOW**: Run enhanced traffic capture (Step 1)
2. **PRIORITY**: Create protocol parser (Step 3)
3. **NEXT**: Set up real-time interception (Step 4)
4. **THEN**: Map GUI actions to messages (Step 5)

The discovery of the unencrypted custom protocol makes this project highly feasible with a clear path to success.
