// find_ssl_strings.js
// Searches for a unique string pattern known to be in OpenSSL

const searchPattern = '77 72 6f 6e 67 20 76 65 72 73 69 6f 6e 20 6e 75 6d 62 65 72'; // "wrong version number"
let found = false;

console.log(`[*] Searching process memory for the OpenSSL string pattern...`);

Process.enumerateRanges('r--').forEach(range => {
  try {
    Memory.scan(range.base, range.size, searchPattern, {
      onMatch: function(address, size) {
        console.log(`[+] SUCCESS: Found OpenSSL fingerprint string at address: ${address}`);
        found = true;
      },
      onError: function(reason) {},
      onComplete: function() {}
    });
  } catch (e) { /* ignore inaccessible memory */ }
});

// After a brief delay, report if nothing was found.
setTimeout(() => {
    if (!found) {
        console.log(`[-] FAILED: Could not find the OpenSSL fingerprint string.`);
    }
}, 3000); // Wait 3 seconds for the scan to complete