// dump_sockets.js
console.log("[*] Setting up socket hooks...");
const MAX = 256;

// Hook recv function
const recvPtr = Module.getExportByName(null, 'recv');
if (recvPtr) {
    console.log("[+] Found recv function, attaching hook...");
    Interceptor.attach(recvPtr, {
        onEnter: function(args) {
            this.socket = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
        },
        onLeave: function(retval) {
            const bytes = retval.toInt32();
            if (bytes > 0) {
                console.log(`\n[<-- RECV on socket ${this.socket} | ${bytes} bytes]`);
                console.log(hexdump(this.buf, {
                    length: Math.min(bytes, MAX),
                    header: true,
                    ansi: true
                }));
            }
        }
    });
} else {
    console.log("[-] Could not find recv function");
}

// Hook send function
const sendPtr = Module.getExportByName(null, 'send');
if (sendPtr) {
    console.log("[+] Found send function, attaching hook...");
    Interceptor.attach(sendPtr, {
        onEnter: function(args) {
            this.socket = args[0].toInt32();
            this.buf = args[1];
            this.len = args[2].toInt32();
            console.log(`\n[--> SEND on socket ${this.socket} | ${this.len} bytes]`);
            console.log(hexdump(this.buf, {
                length: Math.min(this.len, MAX),
                header: true,
                ansi: true
            }));
        }
    });
} else {
    console.log("[-] Could not find send function");
}

console.log("[*] Socket hooks setup complete. Waiting for network activity...");
