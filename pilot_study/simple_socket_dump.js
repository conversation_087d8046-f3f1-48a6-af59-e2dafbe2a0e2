// simple_socket_dump.js
console.log("[*] Starting simple socket dump...");

// Hook recv function
try {
    const recvPtr = Module.getExportByName("libc.so.6", 'recv');
    if (recvPtr) {
        console.log("[+] Found recv function in libc");
        Interceptor.attach(recvPtr, {
            onEnter: function(args) {
                this.socket = args[0].toInt32();
                this.buf = args[1];
                this.len = args[2].toInt32();
            },
            onLeave: function(retval) {
                const bytes = retval.toInt32();
                if (bytes > 0) {
                    console.log(`\n[<-- RECV socket ${this.socket} | ${bytes} bytes]`);
                    console.log(hexdump(this.buf, {
                        length: Math.min(bytes, 256),
                        header: true,
                        ansi: true
                    }));
                }
            }
        });
    } else {
        console.log("[-] recv function not found in libc");
    }
} catch (e) {
    console.log("[-] Could not hook recv: " + e);
}

// Hook send function
try {
    const sendPtr = Module.getExportByName("libc.so.6", 'send');
    if (sendPtr) {
        console.log("[+] Found send function in libc");
        Interceptor.attach(sendPtr, {
            onEnter: function(args) {
                this.socket = args[0].toInt32();
                this.buf = args[1];
                this.len = args[2].toInt32();
                console.log(`\n[--> SEND socket ${this.socket} | ${this.len} bytes]`);
                console.log(hexdump(this.buf, {
                    length: Math.min(this.len, 256),
                    header: true,
                    ansi: true
                }));
            }
        });
    } else {
        console.log("[-] send function not found in libc");
    }
} catch (e) {
    console.log("[-] Could not hook send: " + e);
}

console.log("[*] Socket hooks ready. Waiting for network activity...");
