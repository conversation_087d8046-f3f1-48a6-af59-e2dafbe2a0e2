#!/bin/bash
# enhanced_traffic_capture.sh - Comprehensive Futu traffic analysis

echo "🎯 Enhanced Futu Traffic Capture & Analysis"
echo "==========================================="

# Check if FTNN is running
if ! pgrep -f FTNN > /dev/null; then
    echo "❌ FTNN is not running. Please start it first:"
    echo "   /opt/FTNN/FTNN &"
    exit 1
fi

FTNN_PID=$(pidof FTNN)
echo "✅ Found FTNN process: PID $FTNN_PID"

# Create output directory
mkdir -p futu_analysis
cd futu_analysis

echo ""
echo "📊 Phase 1: Full Traffic Capture (60 seconds)"
echo "============================================="

# Capture comprehensive traffic
echo "Starting comprehensive traffic capture..."
sudo strace -p $FTNN_PID -e trace=sendmsg,recvmsg,send,recv -f -s 1000 -o full_traffic.log &
STRACE_PID=$!

echo "Capturing for 60 seconds... (interact with FTNN GUI now)"
sleep 60

echo "Stopping traffic capture..."
sudo kill $STRACE_PID 2>/dev/null

echo ""
echo "📈 Phase 2: Traffic Analysis"
echo "============================"

# Extract Futu protocol messages
echo "Extracting Futu protocol messages..."
grep "FT'n-X" full_traffic.log > futu_messages.log
FUTU_MSG_COUNT=$(wc -l < futu_messages.log)
echo "Found $FUTU_MSG_COUNT Futu protocol messages"

if [ $FUTU_MSG_COUNT -eq 0 ]; then
    echo "❌ No Futu protocol messages found. Try:"
    echo "   1. Interact more with the FTNN GUI"
    echo "   2. Check if FTNN is actually making network connections"
    echo "   3. Run: netstat -an | grep \$(pidof FTNN)"
    exit 1
fi

# Analyze message patterns
echo "Analyzing message patterns..."
grep -o "FT'n-X[^\"]*" futu_messages.log | sort | uniq -c | sort -nr > message_patterns.txt
echo "Message patterns saved to message_patterns.txt"

# Extract message sizes
echo "Analyzing message sizes..."
awk '/recvmsg.*FT.n-X.*=/ {
    match($0, /= ([0-9]+)/, arr)
    if (arr[1]) print arr[1]
}' futu_messages.log | sort -n | uniq -c > message_sizes.txt
echo "Message sizes saved to message_sizes.txt"

# Extract unique message types (first 20 bytes after header)
echo "Extracting message type signatures..."
grep -o "FT'n-X[^\"]*" futu_messages.log | cut -c1-40 | sort | uniq -c | sort -nr > message_types.txt
echo "Message types saved to message_types.txt"

echo ""
echo "🔍 Phase 3: Protocol Structure Analysis"
echo "======================================="

# Create hex dump of first few messages
echo "Creating hex dumps of sample messages..."
head -5 futu_messages.log | while read line; do
    echo "$line" | grep -o "FT'n-X[^\"]*" | xxd -r -p | xxd
done > sample_hex_dumps.txt 2>/dev/null

echo ""
echo "📋 Analysis Results Summary"
echo "=========================="
echo "Files created in ./futu_analysis/:"
echo "  📄 full_traffic.log      - Complete strace output"
echo "  📄 futu_messages.log     - Futu protocol messages only"
echo "  📄 message_patterns.txt  - Unique message patterns"
echo "  📄 message_sizes.txt     - Message size distribution"
echo "  📄 message_types.txt     - Message type signatures"
echo "  📄 sample_hex_dumps.txt  - Hex dumps of sample messages"

echo ""
echo "🎯 Next Steps:"
echo "1. Review message_patterns.txt to identify different message types"
echo "2. Correlate GUI actions with specific messages in futu_messages.log"
echo "3. Run the protocol parser: python3 ../futu_protocol_parser.py"
echo "4. Set up real-time interception for live analysis"

echo ""
echo "✅ Enhanced traffic capture complete!"
