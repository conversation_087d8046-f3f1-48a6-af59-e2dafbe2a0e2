#!/usr/bin/env python3
"""
Parser Validation Test - Test the enhanced Futu protocol parser
Creates synthetic test data to validate parsing improvements
"""

import binascii
import struct
import tempfile
import os
from futu_protocol_parser import FutuProtocolPars<PERSON>

def create_synthetic_futu_message(msg_type, payload=b''):
    """Create a synthetic Futu protocol message for testing"""
    header = b"FT'n-X\0"
    control_byte = 0x0D
    sequence_id = 12345
    total_length = 24 + len(payload)
    payload_length = len(payload)
    
    message = struct.pack('<7sBIIII',
        header,
        control_byte,
        sequence_id,
        total_length,
        msg_type,
        payload_length
    ) + payload
    
    return message

def create_test_strace_line(message_data):
    """Create a fake strace line with the message data"""
    # Convert binary data to string with escape sequences (like strace does)
    escaped_string = ""
    for byte in message_data:
        if byte == 0:
            escaped_string += "\\0"
        elif byte == 13:
            escaped_string += "\\r"
        elif byte == 10:
            escaped_string += "\\n"
        elif byte == 9:
            escaped_string += "\\t"
        elif 32 <= byte <= 126:  # Printable ASCII
            escaped_string += chr(byte)
        else:
            escaped_string += f"\\{byte:03o}"  # Octal escape

    return f'recvmsg(3, {{msg_name=NULL, msg_namelen=0, msg_iov=[{{iov_base="{escaped_string}", iov_len={len(message_data)}}}], msg_iovlen=1, msg_controllen=0, msg_flags=0}}, 0) = {len(message_data)}\n'

def test_parser_improvements():
    """Test the enhanced parser functionality"""
    print("🧪 TESTING ENHANCED FUTU PROTOCOL PARSER")
    print("=" * 50)
    
    # Test 1: Basic message parsing
    print("\n📋 Test 1: Basic Message Structure Parsing")
    
    test_messages = [
        (0x1001, b'LOGIN_PAYLOAD_TEST'),
        (0x2001, b'QUOTE_SUB_HK.00001'),
        (0x2002, struct.pack('<f', 123.45) + b'PRICE_DATA'),
        (0x2010, b'OPTION_CHAIN_REQUEST'),
        (0x1010, struct.pack('<I', 1640995200))  # Timestamp
    ]
    
    parser = FutuProtocolParser()
    test_results = []
    
    for msg_type, payload in test_messages:
        message = create_synthetic_futu_message(msg_type, payload)
        parsed = parser.parse_futu_message(message)
        
        if parsed:
            print(f"✅ Message type 0x{msg_type:04x}: Parsed successfully")
            print(f"   Sequence ID: {parsed.get('sequence_id', 'N/A')}")
            print(f"   Total Length: {parsed.get('total_length', 'N/A')}")
            print(f"   Msg Type: 0x{parsed.get('msg_type', 0):04x}")
            print(f"   Payload Length: {parsed.get('payload_length', 'N/A')}")
            test_results.append(("PASS", f"msg_type_0x{msg_type:04x}"))
        else:
            print(f"❌ Message type 0x{msg_type:04x}: Parse failed")
            test_results.append(("FAIL", f"msg_type_0x{msg_type:04x}"))
    
    # Test 2: Escape sequence handling
    print("\n📋 Test 2: Escape Sequence Handling")
    
    # Create message with various escape sequences
    payload_with_escapes = b'\x00\x0d\x0a\x09\xab\x7f'  # null, CR, LF, tab, and other bytes
    message = create_synthetic_futu_message(0x9999, payload_with_escapes)
    
    # Create strace line with escapes
    strace_line = create_test_strace_line(message)
    
    # Test extraction
    extracted_data = parser.extract_hex_from_strace(strace_line)
    
    if extracted_data and len(extracted_data) == len(message):
        print("✅ Escape sequence handling: PASS")
        print(f"   Original length: {len(message)}")
        print(f"   Extracted length: {len(extracted_data)}")
        test_results.append(("PASS", "escape_sequences"))
    else:
        print("❌ Escape sequence handling: FAIL")
        print(f"   Original length: {len(message)}")
        print(f"   Extracted length: {len(extracted_data) if extracted_data else 0}")
        test_results.append(("FAIL", "escape_sequences"))
    
    # Test 3: Message classification
    print("\n📋 Test 3: Message Classification")
    
    # Create temporary strace file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
        temp_file = f.name
        
        # Write multiple test messages
        for msg_type, payload in test_messages:
            message = create_synthetic_futu_message(msg_type, payload)
            strace_line = create_test_strace_line(message)
            f.write(strace_line)
    
    try:
        # Analyze the temporary file
        messages = parser.analyze_strace_file(temp_file)
        
        if messages and len(messages) == len(test_messages):
            print(f"✅ Message classification: PASS")
            print(f"   Found {len(messages)} messages")
            
            # Check message types
            found_types = set(msg.get('msg_type', 0) for msg in messages)
            expected_types = set(msg_type for msg_type, _ in test_messages)
            
            if found_types == expected_types:
                print(f"   All message types correctly identified")
                test_results.append(("PASS", "message_classification"))
            else:
                print(f"   Message type mismatch: found {found_types}, expected {expected_types}")
                test_results.append(("FAIL", "message_classification"))
        else:
            print(f"❌ Message classification: FAIL")
            print(f"   Found {len(messages) if messages else 0} messages, expected {len(test_messages)}")
            test_results.append(("FAIL", "message_classification"))
    
    finally:
        # Clean up
        os.unlink(temp_file)
    
    # Test Summary
    print("\n📊 TEST SUMMARY")
    print("-" * 30)
    
    passed = sum(1 for status, _ in test_results if status == "PASS")
    failed = sum(1 for status, _ in test_results if status == "FAIL")
    
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed > 0:
        print("\n❌ Failed Tests:")
        for status, test_name in test_results:
            if status == "FAIL":
                print(f"   - {test_name}")
    
    return passed == len(test_results)

def test_payload_analyzer():
    """Test the payload analyzer with synthetic data"""
    print("\n🔬 TESTING PAYLOAD ANALYZER")
    print("=" * 50)
    
    # Create test messages with known patterns
    test_payloads = [
        # Quote data with ticker and price
        {
            'msg_type': 0x2002,
            'payload': b'HK.00001\x00\x00\x00\x00' + struct.pack('<f', 123.45) + struct.pack('<I', 1000),
            'expected_tickers': ['HK.00001'],
            'expected_prices': [123.45]
        },
        # Option data with multiple strikes
        {
            'msg_type': 0x2011,
            'payload': b'HK.00700\x00\x00\x00\x00' + struct.pack('<fff', 100.0, 110.0, 120.0),
            'expected_tickers': ['HK.00700'],
            'expected_prices': [100.0, 110.0, 120.0]
        },
        # Heartbeat with timestamp
        {
            'msg_type': 0x1010,
            'payload': struct.pack('<I', 1640995200),  # Unix timestamp
            'expected_tickers': [],
            'expected_prices': []
        }
    ]
    
    # Create synthetic JSON data
    synthetic_messages = []
    for i, test_data in enumerate(test_payloads):
        message = {
            'timestamp': '2024-01-01T12:00:00',
            'msg_type': test_data['msg_type'],
            'payload_hex': binascii.hexlify(test_data['payload']).decode(),
            'length': 24 + len(test_data['payload'])
        }
        synthetic_messages.append(message)
    
    # Save to temporary JSON file
    import json
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(synthetic_messages, f, indent=2)
        temp_json = f.name
    
    try:
        # Import and test payload analyzer
        from analyze_payloads import PayloadAnalyzer
        
        analyzer = PayloadAnalyzer()
        messages = analyzer.load_messages(temp_json)
        
        if messages and len(messages) == len(test_payloads):
            print("✅ Payload analyzer loading: PASS")
            
            # Test pattern detection
            for i, msg in enumerate(messages):
                test_data = test_payloads[i]
                
                ascii_patterns = analyzer.find_ascii_patterns(msg['payload_hex'])
                float_patterns = analyzer.find_float_patterns(msg['payload_hex'])
                
                print(f"\n📋 Message {i+1} (type 0x{msg['msg_type']:04x}):")
                print(f"   Tickers found: {ascii_patterns['tickers']}")
                print(f"   Floats found: {[f['value'] for f in float_patterns['floats']]}")
                
                # Validate results
                expected_tickers = test_data['expected_tickers']
                expected_prices = test_data['expected_prices']
                
                tickers_match = set(ascii_patterns['tickers']) >= set(expected_tickers)
                found_prices = [f['value'] for f in float_patterns['floats']]
                prices_match = all(any(abs(fp - ep) < 0.01 for fp in found_prices) for ep in expected_prices)
                
                if tickers_match and (not expected_prices or prices_match):
                    print(f"   ✅ Pattern detection: PASS")
                else:
                    print(f"   ❌ Pattern detection: FAIL")
                    print(f"      Expected tickers: {expected_tickers}")
                    print(f"      Expected prices: {expected_prices}")
        else:
            print("❌ Payload analyzer loading: FAIL")
    
    except ImportError as e:
        print(f"❌ Could not import payload analyzer: {e}")
    except Exception as e:
        print(f"❌ Payload analyzer test failed: {e}")
    
    finally:
        os.unlink(temp_json)

def main():
    """Run all validation tests"""
    print("🚀 FUTU PROTOCOL PARSER VALIDATION SUITE")
    print("=" * 60)
    
    # Test 1: Parser improvements
    parser_success = test_parser_improvements()
    
    # Test 2: Payload analyzer
    test_payload_analyzer()
    
    print("\n🎯 VALIDATION COMPLETE")
    print("=" * 60)
    
    if parser_success:
        print("✅ Enhanced parser is working correctly")
        print("✅ Ready for real protocol analysis")
    else:
        print("❌ Parser validation failed - needs fixes")
    
    print("\n📋 Next Steps:")
    print("1. ✅ Parser validation completed")
    print("2. 🔄 Ready for live traffic capture")
    print("3. 🔄 Ready for message type mapping")
    print("4. 🔄 Ready for replay testing")

if __name__ == "__main__":
    main()
