// crypto_pattern_scan.js - Search for crypto patterns in FTNN binary
console.log("[*] Starting crypto pattern analysis...");

// Common crypto constants and patterns
const cryptoPatterns = {
    // AES S-box (first 16 bytes)
    aes_sbox: "********************************",
    
    // SHA-256 initial hash values
    sha256_h: "6a09e667bb67ae853c6ef372a54ff53a",
    
    // RSA PKCS#1 padding
    rsa_pkcs1: "0001ffffffffffffffffffffffffffff",
    
    // Common TLS/SSL strings
    tls_strings: [
        "TLS_", "SSL_", "HTTPS", "certificate", "handshake",
        "ClientHello", "ServerHello", "CertificateRequest"
    ],
    
    // Crypto library signatures
    crypto_libs: [
        "OpenSSL", "BoringSSL", "mbedTLS", "WolfSSL", "LibreSSL",
        "CryptoPP", "Crypto++", "libcrypto", "libssl"
    ],
    
    // Common cipher names
    ciphers: [
        "AES", "DES", "3DES", "RC4", "ChaCha20", "Salsa20",
        "RSA", "ECDSA", "ECDH", "DH", "DSA"
    ]
};

// Function to search for hex patterns in memory
function searchHexPattern(pattern, moduleName = "FTNN") {
    console.log(`[*] Searching for hex pattern: ${pattern}`);
    
    const module = Process.getModuleByName(moduleName);
    if (!module) {
        console.log(`[-] Module ${moduleName} not found`);
        return [];
    }
    
    const results = [];
    const patternBytes = [];
    
    // Convert hex string to bytes
    for (let i = 0; i < pattern.length; i += 2) {
        patternBytes.push(parseInt(pattern.substr(i, 2), 16));
    }
    
    try {
        const matches = Memory.scanSync(module.base, module.size, pattern.match(/.{2}/g).join(' '));
        matches.forEach(match => {
            results.push({
                address: match.address,
                offset: match.address.sub(module.base)
            });
        });
    } catch (e) {
        console.log(`[-] Error scanning for ${pattern}: ${e}`);
    }
    
    return results;
}

// Function to search for string patterns
function searchStringPatterns(patterns, moduleName = "FTNN") {
    console.log(`[*] Searching for string patterns in ${moduleName}...`);
    
    const module = Process.getModuleByName(moduleName);
    if (!module) {
        console.log(`[-] Module ${moduleName} not found`);
        return {};
    }
    
    const results = {};
    
    patterns.forEach(pattern => {
        try {
            const matches = Memory.scanSync(module.base, module.size, pattern);
            if (matches.length > 0) {
                results[pattern] = matches.map(match => ({
                    address: match.address,
                    offset: match.address.sub(module.base)
                }));
                console.log(`[+] Found "${pattern}" at ${matches.length} location(s)`);
            }
        } catch (e) {
            // Ignore scan errors for string patterns
        }
    });
    
    return results;
}

// Function to dump memory around found patterns
function dumpMemoryContext(address, size = 64) {
    try {
        console.log(`\n[*] Memory dump around ${address}:`);
        console.log(hexdump(address, {
            length: size,
            header: true,
            ansi: true
        }));
    } catch (e) {
        console.log(`[-] Could not dump memory at ${address}: ${e}`);
    }
}

// Main analysis function
function analyzeCryptoPatterns() {
    console.log("[*] Starting comprehensive crypto pattern analysis...");
    
    // Search for hex patterns
    console.log("\n=== HEX PATTERN ANALYSIS ===");
    const hexResults = {};
    
    hexResults.aes_sbox = searchHexPattern(cryptoPatterns.aes_sbox);
    hexResults.sha256_h = searchHexPattern(cryptoPatterns.sha256_h);
    hexResults.rsa_pkcs1 = searchHexPattern(cryptoPatterns.rsa_pkcs1);
    
    // Search for string patterns
    console.log("\n=== STRING PATTERN ANALYSIS ===");
    const tlsResults = searchStringPatterns(cryptoPatterns.tls_strings);
    const libResults = searchStringPatterns(cryptoPatterns.crypto_libs);
    const cipherResults = searchStringPatterns(cryptoPatterns.ciphers);
    
    // Summary
    console.log("\n=== ANALYSIS SUMMARY ===");
    let totalFindings = 0;
    
    Object.keys(hexResults).forEach(key => {
        if (hexResults[key].length > 0) {
            console.log(`[+] ${key}: ${hexResults[key].length} matches`);
            totalFindings += hexResults[key].length;
            
            // Dump first match
            if (hexResults[key][0]) {
                dumpMemoryContext(hexResults[key][0].address);
            }
        }
    });
    
    [tlsResults, libResults, cipherResults].forEach((results, idx) => {
        const categories = ['TLS Strings', 'Crypto Libraries', 'Cipher Names'];
        Object.keys(results).forEach(key => {
            totalFindings += results[key].length;
        });
        if (Object.keys(results).length > 0) {
            console.log(`[+] ${categories[idx]}: ${Object.keys(results).length} different patterns found`);
        }
    });
    
    if (totalFindings === 0) {
        console.log("[-] No crypto patterns found - may indicate custom implementation or obfuscation");
    } else {
        console.log(`[+] Total findings: ${totalFindings} crypto-related patterns`);
    }
    
    return {
        hex: hexResults,
        tls: tlsResults,
        libs: libResults,
        ciphers: cipherResults,
        total: totalFindings
    };
}

// Execute analysis
console.log("[*] Crypto pattern scanner ready. Starting analysis...");
const results = analyzeCryptoPatterns();
console.log("[*] Analysis complete.");
