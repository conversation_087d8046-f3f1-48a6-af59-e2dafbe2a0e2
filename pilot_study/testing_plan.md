# Futu Protocol Testing Plan - Quote & Options Data Extraction

## 🎯 Objective
Extract real-time quote and option data from Futu protocol without trading capabilities.

## 📋 Testing Phases

### Phase 1: Evidence Collection (Day 1)
**Goal**: Capture clean protocol samples for analysis

#### Test 1.1: Login Sequence Capture
```bash
# Terminal 1: Start capture
sudo tcpdump -i any -w login_sequence.pcap 'tcp and host <FUTU_SERVER>'

# Terminal 2: Start FTNN and login
/opt/FTNN/FTNN

# Stop capture after successful login
```

**Expected Results**:
- [ ] Clean pcap file with login handshake
- [ ] Identify authentication message types
- [ ] Document server IP and port

#### Test 1.2: Single Quote Subscription
```bash
# Capture while subscribing to ONE stock quote (e.g., HK.00001)
sudo tcpdump -i any -w quote_subscribe.pcap 'tcp and host <FUTU_SERVER>'
```

**Actions in FTNN**:
1. Click on a single stock quote tab
2. Wait for data to load
3. Close the quote tab

**Expected Results**:
- [ ] Subscribe message identified (msg_type = 0x2001?)
- [ ] Quote data stream captured
- [ ] Unsubscribe message identified

#### Test 1.3: Option Chain Capture
```bash
# Capture option chain request
sudo tcpdump -i any -w option_chain.pcap 'tcp and host <FUTU_SERVER>'
```

**Actions in FTNN**:
1. Open option chain for one stock
2. Select one expiry date
3. Wait for option data to load

**Expected Results**:
- [ ] Option request message type identified
- [ ] Option data format documented
- [ ] Strike prices and IV data captured

#### Test 1.4: Heartbeat/Keep-alive
```bash
# 5-minute idle capture
sudo tcpdump -i any -w heartbeat.pcap 'tcp and host <FUTU_SERVER>'
```

**Actions**: Leave FTNN idle for 5 minutes after login

**Expected Results**:
- [ ] Periodic heartbeat messages identified
- [ ] Heartbeat interval measured
- [ ] Keep-alive response format documented

### Phase 2: Protocol Analysis (Day 2)

#### Test 2.1: Message Structure Validation
```bash
python3 futu_protocol_parser.py login_sequence.pcap
python3 futu_protocol_parser.py quote_subscribe.pcap
python3 futu_protocol_parser.py option_chain.pcap
```

**Validation Checklist**:
- [ ] All messages start with "FT'n-X\0"
- [ ] Length fields match actual message sizes
- [ ] Sequence IDs increment correctly
- [ ] Message types are consistent per action

#### Test 2.2: Field Mapping Verification
Create comparison table:

| Action | msg_type | Payload Size | Key Fields |
|--------|----------|--------------|------------|
| Login | 0x???? | ??? bytes | Username/token? |
| Quote Sub | 0x???? | ??? bytes | Symbol ID |
| Quote Data | 0x???? | ??? bytes | Price/volume |
| Option Req | 0x???? | ??? bytes | Symbol/expiry |
| Heartbeat | 0x???? | ??? bytes | Timestamp? |

#### Test 2.3: Payload Analysis
```python
# Create payload analyzer
python3 analyze_payloads.py parsed_messages.json
```

**Analysis Tasks**:
- [ ] Search for ASCII ticker symbols in payloads
- [ ] Identify IEEE-754 float patterns (prices)
- [ ] Check for compression/encryption
- [ ] Map payload offsets to data fields

### Phase 3: Replay Testing (Day 3)

#### Test 3.1: Session Replay
```python
# Create minimal replay client
python3 session_replay.py --server <IP> --port <PORT>
```

**Test Sequence**:
1. Replay exact login payload
2. Replay quote subscription
3. Verify server responds with quote data

**Success Criteria**:
- [ ] Server accepts replayed login
- [ ] Quote data streams correctly
- [ ] No authentication errors

#### Test 3.2: Symbol Variation Test
```python
# Modify only symbol ID in quote subscription
python3 test_symbol_variation.py --symbol HK.00700
```

**Expected Results**:
- [ ] Server returns data for requested symbol
- [ ] Payload structure remains consistent
- [ ] Different symbols produce different data

#### Test 3.3: Option Chain Replay
```python
# Test option chain requests
python3 test_options.py --symbol HK.00001 --expiry 20240315
```

**Validation**:
- [ ] Option data received for correct expiry
- [ ] Strike prices match GUI display
- [ ] IV and Greeks data present

### Phase 4: Production Client (Day 4)

#### Test 4.1: Continuous Quote Stream
```python
# 30-minute continuous test
python3 quote_streamer.py --symbols HK.00001,HK.00700,HK.00005
```

**Monitoring**:
- [ ] Connection stability (no disconnects)
- [ ] Data freshness (timestamps current)
- [ ] Memory usage stable
- [ ] No trading messages sent

#### Test 4.2: Option Data Extraction
```python
# Extract option chains for multiple symbols
python3 option_extractor.py --symbols HK.00001,HK.00700
```

**Data Validation**:
- [ ] All strike prices captured
- [ ] Bid/ask spreads reasonable
- [ ] Implied volatility values present
- [ ] Expiry dates correct

## 🔧 Testing Tools Required

### 1. Enhanced Parser (futu_protocol_parser.py) ✅
- Improved escape sequence handling
- Better field naming
- Message type classification

### 2. Payload Analyzer
```python
# analyze_payloads.py - to be created
# Searches for patterns in payload data
```

### 3. Session Replay Client
```python
# session_replay.py - to be created
# Replays captured messages to server
```

### 4. Production Streamer
```python
# quote_streamer.py - to be created
# Continuous quote data extraction
```

## 📊 Success Metrics

### Phase 1 Success:
- [ ] 4 clean pcap files captured
- [ ] Server connection details documented
- [ ] Basic message types identified

### Phase 2 Success:
- [ ] Complete message structure mapped
- [ ] Payload formats understood
- [ ] Quote vs option message types separated

### Phase 3 Success:
- [ ] Successful session replay
- [ ] Symbol variation working
- [ ] Option data extraction confirmed

### Phase 4 Success:
- [ ] 30+ minute stable connection
- [ ] Real-time quote data streaming
- [ ] Option chains updating correctly
- [ ] Zero trading messages sent

## 🚨 Safety Measures

### Read-Only Guarantee:
- [ ] Block all 0x3xxx message types (trading)
- [ ] Whitelist only quote/option message types
- [ ] Log all outbound messages for review
- [ ] Implement connection kill-switch

### Testing Environment:
- [ ] Use paper trading account only
- [ ] Monitor network traffic for unexpected messages
- [ ] Keep original FTNN client available as backup
- [ ] Document all server interactions

## 📝 Documentation Requirements

For each test phase:
1. **Input**: What data/actions were used
2. **Output**: Results and observations
3. **Issues**: Problems encountered and solutions
4. **Next Steps**: What to do next

This ensures we have a complete audit trail for the protocol reverse engineering process.
