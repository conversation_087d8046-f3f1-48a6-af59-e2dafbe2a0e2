#!/usr/bin/env python3
"""
Test Execution Framework for Futu Protocol Analysis
Automates the testing plan and documents results
"""

import os
import sys
import json
import subprocess
import time
from datetime import datetime
from pathlib import Path

class TestRunner:
    def __init__(self):
        self.test_results = {}
        self.test_data_dir = Path("test_data")
        self.test_data_dir.mkdir(exist_ok=True)
        
        # Test configuration
        self.config = {
            'server_ip': None,
            'server_port': None,
            'ftnn_path': '/opt/FTNN/FTNN',
            'test_symbols': ['HK.00001', 'HK.00700', 'HK.00005']
        }
    
    def log_test_result(self, test_name, status, details=""):
        """Log test result with timestamp"""
        timestamp = datetime.now().isoformat()
        self.test_results[test_name] = {
            'status': status,
            'timestamp': timestamp,
            'details': details
        }
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
    
    def discover_server_config(self):
        """Discover Futu server IP and port from network connections"""
        print("🔍 Discovering Futu server configuration...")
        
        try:
            # Look for established connections to FTNN process
            result = subprocess.run(['netstat', '-tnp'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            for line in lines:
                if 'FTNN' in line and 'ESTABLISHED' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        remote_addr = parts[4]
                        if ':' in remote_addr:
                            ip, port = remote_addr.rsplit(':', 1)
                            self.config['server_ip'] = ip
                            self.config['server_port'] = int(port)
                            print(f"📡 Found server: {ip}:{port}")
                            return True
            
            print("❌ Could not discover server configuration")
            return False
            
        except Exception as e:
            print(f"❌ Error discovering server: {e}")
            return False
    
    def run_phase1_capture_tests(self):
        """Phase 1: Evidence Collection Tests"""
        print("\n" + "="*60)
        print("📋 PHASE 1: EVIDENCE COLLECTION")
        print("="*60)
        
        if not self.discover_server_config():
            self.log_test_result("Phase1_ServerDiscovery", "FAIL", "Could not find server")
            return False
        
        # Test 1.1: Login Sequence Capture
        print("\n🧪 Test 1.1: Login Sequence Capture")
        print("Instructions:")
        print("1. This will start packet capture")
        print("2. Start FTNN and login when prompted")
        print("3. Press Enter when login is complete")
        
        input("Press Enter to start capture...")
        
        capture_file = self.test_data_dir / "login_sequence.pcap"
        
        # Start tcpdump
        tcpdump_cmd = [
            'sudo', 'tcpdump', '-i', 'any', '-w', str(capture_file),
            f'tcp and host {self.config["server_ip"]}'
        ]
        
        try:
            print("📡 Starting packet capture...")
            tcpdump_proc = subprocess.Popen(tcpdump_cmd)
            
            print("🚀 Now start FTNN and login...")
            input("Press Enter when login is complete...")
            
            # Stop capture
            tcpdump_proc.terminate()
            time.sleep(2)
            
            if capture_file.exists() and capture_file.stat().st_size > 0:
                self.log_test_result("Test1.1_LoginCapture", "PASS", f"Captured to {capture_file}")
            else:
                self.log_test_result("Test1.1_LoginCapture", "FAIL", "No data captured")
                
        except Exception as e:
            self.log_test_result("Test1.1_LoginCapture", "FAIL", str(e))
        
        # Test 1.2: Quote Subscription Capture
        print("\n🧪 Test 1.2: Quote Subscription Capture")
        self.run_quote_capture_test()
        
        # Test 1.3: Option Chain Capture
        print("\n🧪 Test 1.3: Option Chain Capture")
        self.run_option_capture_test()
        
        # Test 1.4: Heartbeat Capture
        print("\n🧪 Test 1.4: Heartbeat Capture")
        self.run_heartbeat_capture_test()
    
    def run_quote_capture_test(self):
        """Capture quote subscription messages"""
        capture_file = self.test_data_dir / "quote_subscribe.pcap"
        
        print("Instructions:")
        print("1. Packet capture will start")
        print("2. Click on ONE stock quote tab (e.g., HK.00001)")
        print("3. Wait for data to load")
        print("4. Close the quote tab")
        print("5. Press Enter when done")
        
        input("Press Enter to start capture...")
        
        tcpdump_cmd = [
            'sudo', 'tcpdump', '-i', 'any', '-w', str(capture_file),
            f'tcp and host {self.config["server_ip"]}'
        ]
        
        try:
            tcpdump_proc = subprocess.Popen(tcpdump_cmd)
            print("📡 Capture started. Perform quote subscription actions...")
            input("Press Enter when done...")
            
            tcpdump_proc.terminate()
            time.sleep(2)
            
            if capture_file.exists() and capture_file.stat().st_size > 0:
                self.log_test_result("Test1.2_QuoteCapture", "PASS", f"Captured to {capture_file}")
            else:
                self.log_test_result("Test1.2_QuoteCapture", "FAIL", "No data captured")
                
        except Exception as e:
            self.log_test_result("Test1.2_QuoteCapture", "FAIL", str(e))
    
    def run_option_capture_test(self):
        """Capture option chain messages"""
        capture_file = self.test_data_dir / "option_chain.pcap"
        
        print("Instructions:")
        print("1. Packet capture will start")
        print("2. Open option chain for ONE stock")
        print("3. Select ONE expiry date")
        print("4. Wait for option data to load")
        print("5. Press Enter when done")
        
        input("Press Enter to start capture...")
        
        tcpdump_cmd = [
            'sudo', 'tcpdump', '-i', 'any', '-w', str(capture_file),
            f'tcp and host {self.config["server_ip"]}'
        ]
        
        try:
            tcpdump_proc = subprocess.Popen(tcpdump_cmd)
            print("📡 Capture started. Perform option chain actions...")
            input("Press Enter when done...")
            
            tcpdump_proc.terminate()
            time.sleep(2)
            
            if capture_file.exists() and capture_file.stat().st_size > 0:
                self.log_test_result("Test1.3_OptionCapture", "PASS", f"Captured to {capture_file}")
            else:
                self.log_test_result("Test1.3_OptionCapture", "FAIL", "No data captured")
                
        except Exception as e:
            self.log_test_result("Test1.3_OptionCapture", "FAIL", str(e))
    
    def run_heartbeat_capture_test(self):
        """Capture heartbeat/keep-alive messages"""
        capture_file = self.test_data_dir / "heartbeat.pcap"
        
        print("Instructions:")
        print("1. Packet capture will start")
        print("2. Leave FTNN idle for 5 minutes")
        print("3. Do NOT interact with the GUI")
        
        input("Press Enter to start 5-minute capture...")
        
        tcpdump_cmd = [
            'sudo', 'tcpdump', '-i', 'any', '-w', str(capture_file),
            f'tcp and host {self.config["server_ip"]}'
        ]
        
        try:
            tcpdump_proc = subprocess.Popen(tcpdump_cmd)
            print("📡 5-minute idle capture started...")
            
            # Wait 5 minutes
            for i in range(300, 0, -30):
                print(f"⏰ {i} seconds remaining...")
                time.sleep(30)
            
            tcpdump_proc.terminate()
            time.sleep(2)
            
            if capture_file.exists() and capture_file.stat().st_size > 0:
                self.log_test_result("Test1.4_HeartbeatCapture", "PASS", f"Captured to {capture_file}")
            else:
                self.log_test_result("Test1.4_HeartbeatCapture", "FAIL", "No data captured")
                
        except Exception as e:
            self.log_test_result("Test1.4_HeartbeatCapture", "FAIL", str(e))
    
    def run_phase2_analysis_tests(self):
        """Phase 2: Protocol Analysis Tests"""
        print("\n" + "="*60)
        print("📊 PHASE 2: PROTOCOL ANALYSIS")
        print("="*60)
        
        # Convert pcap files to parseable format using tshark
        pcap_files = list(self.test_data_dir.glob("*.pcap"))
        
        for pcap_file in pcap_files:
            print(f"\n🔍 Analyzing {pcap_file.name}...")
            
            # Extract TCP payload using tshark
            strace_file = pcap_file.with_suffix('.log')
            
            try:
                # Extract TCP data that contains FT'n-X
                tshark_cmd = [
                    'tshark', '-r', str(pcap_file),
                    '-Y', 'tcp.payload contains "FT"',
                    '-T', 'fields',
                    '-e', 'tcp.payload'
                ]
                
                result = subprocess.run(tshark_cmd, capture_output=True, text=True)
                
                if result.returncode == 0 and result.stdout.strip():
                    # Convert hex output to strace-like format
                    with open(strace_file, 'w') as f:
                        for line in result.stdout.strip().split('\n'):
                            if line.strip():
                                # Convert hex to binary and create fake strace line
                                hex_data = line.replace(':', '')
                                f.write(f'recvmsg(3, {{msg_name=NULL, msg_namelen=0, msg_iov=[{{iov_base="{hex_data}", iov_len=1024}}], msg_iovlen=1, msg_controllen=0, msg_flags=0}}, 0) = 1024\n')
                    
                    # Run parser on the converted file
                    if strace_file.exists():
                        self.run_parser_analysis(strace_file)
                
            except Exception as e:
                self.log_test_result(f"Analysis_{pcap_file.stem}", "FAIL", str(e))
    
    def run_parser_analysis(self, strace_file):
        """Run the protocol parser on a strace file"""
        try:
            result = subprocess.run([
                'python3', 'futu_protocol_parser.py', str(strace_file)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                # Check if JSON was created
                json_file = Path("parsed_messages.json")
                if json_file.exists():
                    # Run payload analysis
                    payload_result = subprocess.run([
                        'python3', 'analyze_payloads.py', str(json_file)
                    ], capture_output=True, text=True)
                    
                    self.log_test_result(f"Parser_{strace_file.stem}", "PASS", "Analysis completed")
                else:
                    self.log_test_result(f"Parser_{strace_file.stem}", "FAIL", "No JSON output")
            else:
                self.log_test_result(f"Parser_{strace_file.stem}", "FAIL", result.stderr)
                
        except Exception as e:
            self.log_test_result(f"Parser_{strace_file.stem}", "FAIL", str(e))
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        report_file = self.test_data_dir / "test_report.json"
        
        report = {
            'test_run_timestamp': datetime.now().isoformat(),
            'configuration': self.config,
            'test_results': self.test_results,
            'summary': {
                'total_tests': len(self.test_results),
                'passed': len([r for r in self.test_results.values() if r['status'] == 'PASS']),
                'failed': len([r for r in self.test_results.values() if r['status'] == 'FAIL']),
                'warnings': len([r for r in self.test_results.values() if r['status'] == 'WARN'])
            }
        }
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📋 Test report saved to {report_file}")
        
        # Print summary
        print("\n" + "="*60)
        print("📊 TEST SUMMARY")
        print("="*60)
        print(f"Total Tests: {report['summary']['total_tests']}")
        print(f"✅ Passed: {report['summary']['passed']}")
        print(f"❌ Failed: {report['summary']['failed']}")
        print(f"⚠️  Warnings: {report['summary']['warnings']}")
        
        if report['summary']['failed'] > 0:
            print("\n❌ Failed Tests:")
            for test_name, result in self.test_results.items():
                if result['status'] == 'FAIL':
                    print(f"  - {test_name}: {result['details']}")

def main():
    print("🧪 FUTU PROTOCOL TEST SUITE")
    print("="*60)
    print("This will run comprehensive tests to reverse engineer")
    print("the Futu protocol for quote and option data extraction.")
    print()
    print("⚠️  SAFETY NOTICE:")
    print("- Only quote/option data will be extracted")
    print("- No trading messages will be sent")
    print("- Use paper trading account only")
    print()
    
    response = input("Continue with testing? (y/N): ")
    if response.lower() != 'y':
        print("❌ Testing cancelled")
        sys.exit(0)
    
    runner = TestRunner()
    
    try:
        # Run test phases
        runner.run_phase1_capture_tests()
        runner.run_phase2_analysis_tests()
        
    finally:
        # Always generate report
        runner.generate_test_report()

if __name__ == "__main__":
    main()
