// function_discovery.js - Discover available functions
console.log("[*] Discovering available functions...");

// List all exports from main module
console.log("\n=== FTNN EXPORTS ===");
try {
    const ftnn = Process.getModuleByName("FTNN");
    const exports = ftnn.enumerateExports();
    console.log(`[*] FTNN has ${exports.length} exports`);
    
    // Look for network-related exports
    const networkFuncs = exports.filter(exp => 
        exp.name.toLowerCase().includes('send') ||
        exp.name.toLowerCase().includes('recv') ||
        exp.name.toLowerCase().includes('socket') ||
        exp.name.toLowerCase().includes('connect') ||
        exp.name.toLowerCase().includes('net') ||
        exp.name.toLowerCase().includes('tcp') ||
        exp.name.toLowerCase().includes('http')
    );
    
    if (networkFuncs.length > 0) {
        console.log("[+] Network-related functions found:");
        networkFuncs.forEach(func => {
            console.log(`  - ${func.name} @ ${func.address}`);
        });
    } else {
        console.log("[-] No obvious network functions in FTNN exports");
    }
} catch (e) {
    console.log("[-] Could not enumerate FTNN exports: " + e);
}

// List all exports from libc
console.log("\n=== LIBC EXPORTS ===");
try {
    const libc = Process.getModuleByName("libc.so.6");
    const exports = libc.enumerateExports();
    
    const networkFuncs = ['send', 'recv', 'socket', 'connect', 'read', 'write', 'sendto', 'recvfrom'];
    console.log("[*] Checking for standard network functions in libc:");
    
    networkFuncs.forEach(funcName => {
        const func = exports.find(exp => exp.name === funcName);
        if (func) {
            console.log(`[+] ${funcName} @ ${func.address}`);
        } else {
            console.log(`[-] ${funcName} not found`);
        }
    });
} catch (e) {
    console.log("[-] Could not enumerate libc exports: " + e);
}

// Try to hook using addresses directly
console.log("\n=== DIRECT HOOKING ATTEMPT ===");
const networkFunctions = ['send', 'recv', 'socket', 'connect'];

networkFunctions.forEach(funcName => {
    try {
        // Try multiple approaches
        let funcPtr = null;
        
        // Method 1: Default search
        try {
            funcPtr = Module.getExportByName(null, funcName);
            if (funcPtr) {
                console.log(`[+] Found ${funcName} via default search @ ${funcPtr}`);
            }
        } catch (e) {}
        
        // Method 2: libc search
        if (!funcPtr) {
            try {
                funcPtr = Module.getExportByName("libc.so.6", funcName);
                if (funcPtr) {
                    console.log(`[+] Found ${funcName} in libc @ ${funcPtr}`);
                }
            } catch (e) {}
        }
        
        // Method 3: Symbol resolution
        if (!funcPtr) {
            try {
                funcPtr = Module.findExportByName(null, funcName);
                if (funcPtr) {
                    console.log(`[+] Found ${funcName} via symbol resolution @ ${funcPtr}`);
                }
            } catch (e) {}
        }
        
        if (funcPtr) {
            console.log(`[*] Attempting to hook ${funcName}...`);
            Interceptor.attach(funcPtr, {
                onEnter: function(args) {
                    console.log(`[HOOK] ${funcName} called with args: ${args[0]}, ${args[1]}, ${args[2]}`);
                },
                onLeave: function(retval) {
                    console.log(`[HOOK] ${funcName} returned: ${retval}`);
                }
            });
            console.log(`[+] Successfully hooked ${funcName}`);
        } else {
            console.log(`[-] Could not find ${funcName}`);
        }
        
    } catch (e) {
        console.log(`[-] Error hooking ${funcName}: ${e}`);
    }
});

// Alternative: Monitor file descriptors
console.log("\n=== FILE DESCRIPTOR MONITORING ===");
try {
    // Hook open() to see what files/sockets are opened
    const openPtr = Module.getExportByName(null, 'open');
    if (openPtr) {
        console.log("[+] Hooking open() for FD monitoring");
        Interceptor.attach(openPtr, {
            onEnter: function(args) {
                const path = args[0].readUtf8String();
                console.log(`[OPEN] ${path}`);
            },
            onLeave: function(retval) {
                const fd = retval.toInt32();
                if (fd > 0) {
                    console.log(`[OPEN] FD: ${fd}`);
                }
            }
        });
    }
} catch (e) {
    console.log("[-] Could not hook open: " + e);
}

console.log("\n[*] Function discovery complete. Monitoring activity...");
