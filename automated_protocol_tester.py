#!/usr/bin/env python3
"""
Automated Protocol Tester - Records real FTNN traffic and analyzes it
Waits for user to perform actions, then captures and analyzes the results
"""

import subprocess
import time
import signal
import os
import json
from datetime import datetime
from pathlib import Path

class ProtocolTester:
    def __init__(self):
        self.test_data_dir = Path("test_data")
        self.test_data_dir.mkdir(exist_ok=True)
        self.test_results = {}
        self.capture_processes = {}
        
    def log_result(self, test_name, status, details=""):
        """Log test result with timestamp"""
        timestamp = datetime.now().isoformat()
        self.test_results[test_name] = {
            'status': status,
            'timestamp': timestamp,
            'details': details
        }
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status}")
        if details:
            print(f"   📋 {details}")
    
    def discover_ftnn_connections(self):
        """Find active FTNN connections"""
        print("🔍 Discovering FTNN network connections...")
        
        try:
            # Check if FTNN is running
            ps_result = subprocess.run(['pgrep', '-f', 'FTNN'], capture_output=True, text=True)
            if not ps_result.stdout.strip():
                print("❌ FTNN is not running")
                print("💡 Please start FTNN first: /opt/FTNN/FTNN")
                return None
            
            print("✅ FTNN is running")
            
            # Find network connections using ss (modern replacement for netstat)
            try:
                ss_result = subprocess.run(['ss', '-tnp'], capture_output=True, text=True)
                netstat_output = ss_result.stdout
            except FileNotFoundError:
                # Fallback to lsof if ss not available
                try:
                    lsof_result = subprocess.run(['lsof', '-i', '-n'], capture_output=True, text=True)
                    netstat_output = lsof_result.stdout
                except FileNotFoundError:
                    print("❌ No network tools available (netstat, ss, or lsof)")
                    return None

            connections = []
            for line in netstat_output.split('\n'):
                if 'FTNN' in line and ('ESTABLISHED' in line or 'ESTAB' in line):
                    # Parse different formats
                    if 'ESTAB' in line:  # ss format
                        parts = line.split()
                        if len(parts) >= 4:
                            local_addr = parts[3]
                            remote_addr = parts[4] if len(parts) > 4 else parts[3]
                    else:  # lsof format
                        parts = line.split()
                        for part in parts:
                            if '->' in part:
                                local_addr, remote_addr = part.split('->')
                                break
                        else:
                            continue

                    if ':' in remote_addr:
                        ip, port = remote_addr.rsplit(':', 1)
                        try:
                            connections.append({
                                'local': local_addr,
                                'remote': remote_addr,
                                'server_ip': ip,
                                'server_port': int(port)
                            })
                        except ValueError:
                            continue
            
            if connections:
                print(f"📡 Found {len(connections)} FTNN connections:")
                for i, conn in enumerate(connections):
                    print(f"   {i+1}. {conn['remote']}")
                return connections[0]  # Use first connection
            else:
                print("❌ No FTNN network connections found")
                print("💡 Please login to FTNN to establish server connection")
                return None
                
        except Exception as e:
            print(f"❌ Error discovering connections: {e}")
            return None
    
    def start_traffic_capture(self, test_name, server_ip):
        """Start capturing network traffic"""
        capture_file = self.test_data_dir / f"{test_name}.pcap"
        
        # Use tcpdump to capture traffic
        tcpdump_cmd = [
            'sudo', 'tcpdump', '-i', 'any', '-w', str(capture_file),
            '-s', '0',  # Capture full packets
            f'host {server_ip}'
        ]
        
        try:
            print(f"📡 Starting traffic capture for {test_name}...")
            proc = subprocess.Popen(tcpdump_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            self.capture_processes[test_name] = {
                'process': proc,
                'file': capture_file,
                'start_time': time.time()
            }
            time.sleep(2)  # Let tcpdump start
            print(f"✅ Capture started - saving to {capture_file}")
            return True
        except Exception as e:
            print(f"❌ Failed to start capture: {e}")
            return False
    
    def stop_traffic_capture(self, test_name):
        """Stop traffic capture and return captured data info"""
        if test_name not in self.capture_processes:
            return None
        
        capture_info = self.capture_processes[test_name]
        proc = capture_info['process']
        capture_file = capture_info['file']
        
        try:
            # Stop tcpdump gracefully
            proc.send_signal(signal.SIGTERM)
            proc.wait(timeout=5)
        except subprocess.TimeoutExpired:
            proc.kill()
        
        # Check if we captured data
        if capture_file.exists() and capture_file.stat().st_size > 0:
            duration = time.time() - capture_info['start_time']
            size = capture_file.stat().st_size
            print(f"✅ Capture stopped - {size} bytes captured in {duration:.1f}s")
            
            del self.capture_processes[test_name]
            return {
                'file': capture_file,
                'size': size,
                'duration': duration
            }
        else:
            print(f"❌ No data captured")
            del self.capture_processes[test_name]
            return None
    
    def analyze_captured_traffic(self, capture_file):
        """Analyze captured pcap file for Futu protocol messages"""
        print(f"🔍 Analyzing captured traffic: {capture_file}")

        # Try tshark first, then fallback to tcpdump
        packets = []

        try:
            # Try tshark if available
            tshark_cmd = [
                'tshark', '-r', str(capture_file),
                '-Y', 'tcp.len > 0',
                '-T', 'fields',
                '-e', 'frame.time',
                '-e', 'tcp.srcport',
                '-e', 'tcp.dstport',
                '-e', 'tcp.payload',
                '-E', 'separator=|'
            ]

            result = subprocess.run(tshark_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                packets = self._parse_tshark_output(result.stdout)
            else:
                raise Exception("tshark failed")

        except (FileNotFoundError, Exception):
            print("⚠️ tshark not available, trying tcpdump...")
            try:
                # Fallback to tcpdump with hex output
                tcpdump_cmd = [
                    'tcpdump', '-r', str(capture_file),
                    '-x', '-n', '-q'
                ]

                result = subprocess.run(tcpdump_cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    packets = self._parse_tcpdump_output(result.stdout)
                else:
                    print(f"❌ tcpdump failed: {result.stderr}")
                    return None

            except Exception as e:
                print(f"❌ Analysis failed: {e}")
                return None

        futu_packets = len([p for p in packets if p.get('payload_bytes', b'').startswith(b"FT'n-X")])

        print(f"📊 Analysis complete:")
        print(f"   🔍 Found {futu_packets} Futu protocol packets")
        print(f"   📦 Total analyzable packets: {len(packets)}")

        return packets

    def _parse_tshark_output(self, output):
        """Parse tshark output"""
        packets = []
        for line in output.strip().split('\n'):
            if not line.strip():
                continue

            parts = line.split('|')
            if len(parts) >= 4:
                timestamp, src_port, dst_port, payload_hex = parts[:4]

                if payload_hex and payload_hex.strip():
                    try:
                        payload_bytes = bytes.fromhex(payload_hex.replace(':', ''))
                        packets.append({
                            'timestamp': timestamp,
                            'src_port': src_port,
                            'dst_port': dst_port,
                            'payload_hex': payload_hex.replace(':', ''),
                            'payload_bytes': payload_bytes
                        })
                    except ValueError:
                        continue
        return packets

    def _parse_tcpdump_output(self, output):
        """Parse tcpdump hex output"""
        packets = []
        current_packet = None
        hex_data = ""

        for line in output.split('\n'):
            line = line.strip()
            if not line:
                continue

            # New packet line
            if line.startswith(('IP ', 'IP6 ')) or '>' in line:
                # Save previous packet if it has data
                if current_packet and hex_data:
                    try:
                        payload_bytes = bytes.fromhex(hex_data.replace(' ', ''))
                        current_packet['payload_hex'] = hex_data.replace(' ', '')
                        current_packet['payload_bytes'] = payload_bytes
                        packets.append(current_packet)
                    except ValueError:
                        pass

                # Start new packet
                current_packet = {
                    'timestamp': 'unknown',
                    'src_port': 'unknown',
                    'dst_port': 'unknown'
                }
                hex_data = ""

            # Hex data line
            elif line.startswith(('0x', '\t0x')):
                # Extract hex data (skip offset)
                parts = line.split()
                if len(parts) > 1:
                    hex_data += ' '.join(parts[1:])

        # Don't forget the last packet
        if current_packet and hex_data:
            try:
                payload_bytes = bytes.fromhex(hex_data.replace(' ', ''))
                current_packet['payload_hex'] = hex_data.replace(' ', '')
                current_packet['payload_bytes'] = payload_bytes
                packets.append(current_packet)
            except ValueError:
                pass

        return packets
    
    def parse_futu_packets(self, packets):
        """Parse Futu protocol packets using our enhanced parser"""
        if not packets:
            return []
        
        print("🔬 Parsing Futu protocol messages...")
        
        # Create temporary strace-style file
        temp_file = self.test_data_dir / "temp_strace.log"
        
        try:
            with open(temp_file, 'w') as f:
                for i, packet in enumerate(packets):
                    payload_hex = packet['payload_hex']
                    # Convert to strace-style format
                    f.write(f'recvmsg(3, {{msg_name=NULL, msg_namelen=0, msg_iov=[{{iov_base="{payload_hex}", iov_len={len(payload_hex)//2}}}], msg_iovlen=1, msg_controllen=0, msg_flags=0}}, 0) = {len(payload_hex)//2}\n')
            
            # Use our parser
            from futu_protocol_parser import FutuProtocolParser
            parser = FutuProtocolParser()
            
            # Parse as hex data directly
            parsed_messages = []
            for packet in packets:
                try:
                    payload_bytes = packet['payload_bytes']
                    parsed = parser.parse_futu_message(payload_bytes)
                    if parsed:
                        parsed['capture_timestamp'] = packet['timestamp']
                        parsed['src_port'] = packet['src_port']
                        parsed['dst_port'] = packet['dst_port']
                        parsed_messages.append(parsed)
                except Exception as e:
                    print(f"⚠️ Failed to parse packet: {e}")
                    continue
            
            print(f"✅ Successfully parsed {len(parsed_messages)} messages")
            return parsed_messages
            
        except Exception as e:
            print(f"❌ Parsing failed: {e}")
            return []
        finally:
            if temp_file.exists():
                temp_file.unlink()
    
    def run_interactive_test(self, test_name, instructions):
        """Run an interactive test with user actions"""
        print(f"\n{'='*60}")
        print(f"🧪 TEST: {test_name}")
        print(f"{'='*60}")
        
        # Discover FTNN connections
        connection = self.discover_ftnn_connections()
        if not connection:
            self.log_result(test_name, "FAIL", "No FTNN connection found")
            return None
        
        server_ip = connection['server_ip']
        print(f"📡 Using server: {server_ip}")
        
        # Show instructions
        print(f"\n📋 INSTRUCTIONS:")
        for i, instruction in enumerate(instructions, 1):
            print(f"   {i}. {instruction}")
        
        print(f"\n⏳ Press Enter when ready to start capture...")
        input()
        
        # Start capture
        if not self.start_traffic_capture(test_name, server_ip):
            self.log_result(test_name, "FAIL", "Failed to start traffic capture")
            return None
        
        print(f"\n🎬 CAPTURE IS RUNNING - Perform the actions now!")
        print(f"⏳ Press Enter when you've completed all actions...")
        input()
        
        # Stop capture
        capture_info = self.stop_traffic_capture(test_name)
        if not capture_info:
            self.log_result(test_name, "FAIL", "No traffic captured")
            return None
        
        # Analyze captured traffic
        packets = self.analyze_captured_traffic(capture_info['file'])
        if not packets:
            self.log_result(test_name, "WARN", "No Futu protocol packets found")
            return None
        
        # Parse messages
        messages = self.parse_futu_packets(packets)
        if not messages:
            self.log_result(test_name, "WARN", "No parseable messages found")
            return None
        
        # Save results
        results_file = self.test_data_dir / f"{test_name}_results.json"
        with open(results_file, 'w') as f:
            json.dump({
                'test_name': test_name,
                'timestamp': datetime.now().isoformat(),
                'capture_info': {
                    'file': str(capture_info['file']),
                    'size': capture_info['size'],
                    'duration': capture_info['duration']
                },
                'packets_found': len(packets),
                'messages_parsed': len(messages),
                'messages': messages
            }, f, indent=2, default=str)
        
        self.log_result(test_name, "PASS", f"Captured {len(messages)} messages -> {results_file}")
        
        # Show summary
        msg_types = {}
        for msg in messages:
            msg_type = msg.get('msg_type', 0)
            msg_types[msg_type] = msg_types.get(msg_type, 0) + 1
        
        print(f"\n📊 MESSAGE TYPES FOUND:")
        for msg_type, count in sorted(msg_types.items()):
            print(f"   0x{msg_type:08x}: {count} messages")
        
        return messages

def main():
    print("🚀 FUTU PROTOCOL AUTOMATED TESTER")
    print("="*60)
    print("This tool captures REAL network traffic while you use FTNN")
    print("and analyzes the Futu protocol messages.")
    print()
    
    tester = ProtocolTester()
    
    # Test scenarios
    test_scenarios = [
        {
            'name': 'login_sequence',
            'instructions': [
                'Start FTNN if not already running',
                'Login with your credentials',
                'Wait for main interface to load completely'
            ]
        },
        {
            'name': 'quote_subscription',
            'instructions': [
                'Click on a stock quote (e.g., HK.00001)',
                'Wait for quote data to load',
                'Close the quote window'
            ]
        },
        {
            'name': 'option_chain',
            'instructions': [
                'Open option chain for a stock',
                'Select an expiry date',
                'Wait for option data to load'
            ]
        },
        {
            'name': 'heartbeat_idle',
            'instructions': [
                'Leave FTNN idle (do not click anything)',
                'Wait for 2 minutes to capture heartbeat messages'
            ]
        }
    ]
    
    print("Available tests:")
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"  {i}. {scenario['name']}")
    
    print("\nEnter test numbers to run (comma-separated), or 'all' for all tests:")
    choice = input("Choice: ").strip()
    
    if choice.lower() == 'all':
        selected_tests = test_scenarios
    else:
        try:
            indices = [int(x.strip()) - 1 for x in choice.split(',')]
            selected_tests = [test_scenarios[i] for i in indices if 0 <= i < len(test_scenarios)]
        except (ValueError, IndexError):
            print("❌ Invalid selection")
            return
    
    # Run selected tests
    all_results = {}
    for scenario in selected_tests:
        result = tester.run_interactive_test(scenario['name'], scenario['instructions'])
        all_results[scenario['name']] = result
    
    # Generate final report
    print(f"\n{'='*60}")
    print("📊 FINAL TEST REPORT")
    print(f"{'='*60}")
    
    for test_name, result in tester.test_results.items():
        status_emoji = "✅" if result['status'] == "PASS" else "❌" if result['status'] == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {result['status']}")
        if result['details']:
            print(f"   {result['details']}")
    
    print(f"\n📁 All results saved in: {tester.test_data_dir}")
    print("🎯 Next steps:")
    print("1. Review the captured message types")
    print("2. Correlate message types with your actions")
    print("3. Use session_replay.py to test message replay")

if __name__ == "__main__":
    main()
