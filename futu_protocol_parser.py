#!/usr/bin/env python3
"""
Futu Protocol Parser - Decode and analyze Futu "FT'n-X" protocol messages
"""

import struct
import re
import sys
import binascii
from datetime import datetime

class FutuProtocolParser:
    def __init__(self):
        self.message_types = {}
        self.parsed_messages = []
        
    def extract_hex_from_strace(self, strace_line):
        """Extract hex data from strace output line"""
        # Look for the pattern: iov_base="..."
        match = re.search(r'iov_base="([^"]*)"', strace_line)
        if not match:
            return None
            
        raw_data = match.group(1)
        
        # Convert escape sequences to bytes
        try:
            # Handle common escape sequences
            raw_data = raw_data.replace('\\0', '\x00')
            raw_data = raw_data.replace('\\r', '\x0d')
            raw_data = raw_data.replace('\\n', '\x0a')
            raw_data = raw_data.replace('\\t', '\x09')
            
            # Convert to bytes
            return raw_data.encode('latin1')
        except Exception as e:
            print(f"Error converting data: {e}")
            return None
    
    def parse_futu_message(self, data):
        """Parse a Futu protocol message"""
        if len(data) < 7 or not data.startswith(b"FT'n-X"):
            return None
            
        try:
            result = {
                'raw_data': data,
                'length': len(data),
                'header': data[:7].decode('ascii', errors='ignore'),
                'timestamp': datetime.now().isoformat()
            }
            
            if len(data) >= 8:
                result['control_byte'] = data[7]
                
            if len(data) >= 12:
                result['field1'] = struct.unpack('<I', data[8:12])[0]
                
            if len(data) >= 16:
                result['field2'] = struct.unpack('<I', data[12:16])[0]
                
            if len(data) >= 20:
                result['field3'] = struct.unpack('<I', data[16:20])[0]
                
            if len(data) >= 24:
                result['field4'] = struct.unpack('<I', data[20:24])[0]
                
            # Extract payload
            if len(data) > 24:
                result['payload'] = data[24:]
                result['payload_hex'] = binascii.hexlify(data[24:]).decode()
                
            # Create message signature for classification
            if len(data) >= 20:
                signature = struct.pack('<III', 
                    result.get('field1', 0),
                    result.get('field2', 0), 
                    result.get('field3', 0))
                result['signature'] = binascii.hexlify(signature).decode()
                
            return result
            
        except Exception as e:
            print(f"Error parsing message: {e}")
            return None
    
    def analyze_strace_file(self, filename):
        """Analyze a strace log file for Futu messages"""
        print(f"🔍 Analyzing {filename}...")
        
        futu_messages = []
        
        try:
            with open(filename, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    if "FT'n-X" in line and "recvmsg" in line:
                        data = self.extract_hex_from_strace(line)
                        if data:
                            parsed = self.parse_futu_message(data)
                            if parsed:
                                parsed['line_number'] = line_num
                                parsed['strace_line'] = line.strip()
                                futu_messages.append(parsed)
                                
        except FileNotFoundError:
            print(f"❌ File {filename} not found")
            return []
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            return []
            
        print(f"✅ Found {len(futu_messages)} Futu protocol messages")
        return futu_messages
    
    def classify_messages(self, messages):
        """Classify messages by type/signature"""
        classifications = {}
        
        for msg in messages:
            sig = msg.get('signature', 'unknown')
            if sig not in classifications:
                classifications[sig] = []
            classifications[sig].append(msg)
            
        return classifications
    
    def print_analysis_report(self, messages):
        """Print detailed analysis report"""
        print("\n" + "="*60)
        print("📊 FUTU PROTOCOL ANALYSIS REPORT")
        print("="*60)
        
        if not messages:
            print("❌ No messages to analyze")
            return
            
        print(f"📈 Total Messages: {len(messages)}")
        
        # Message size distribution
        sizes = [msg['length'] for msg in messages]
        print(f"📏 Message Sizes: {min(sizes)}-{max(sizes)} bytes (avg: {sum(sizes)/len(sizes):.1f})")
        
        # Classify by signature
        classifications = self.classify_messages(messages)
        print(f"🏷️  Message Types: {len(classifications)} unique signatures")
        
        print("\n" + "-"*40)
        print("MESSAGE TYPE BREAKDOWN")
        print("-"*40)
        
        for sig, msgs in sorted(classifications.items(), key=lambda x: len(x[1]), reverse=True):
            print(f"Signature {sig}: {len(msgs)} messages")
            
            # Show sample message details
            sample = msgs[0]
            print(f"  Sample: {sample['length']} bytes")
            print(f"  Control: 0x{sample.get('control_byte', 0):02x}")
            print(f"  Field1:  0x{sample.get('field1', 0):08x}")
            print(f"  Field2:  0x{sample.get('field2', 0):08x}")
            print(f"  Field3:  0x{sample.get('field3', 0):08x}")
            if 'payload_hex' in sample and sample['payload_hex']:
                payload_preview = sample['payload_hex'][:40] + ("..." if len(sample['payload_hex']) > 40 else "")
                print(f"  Payload: {payload_preview}")
            print()
            
        print("-"*40)
        print("SAMPLE MESSAGES (First 3)")
        print("-"*40)
        
        for i, msg in enumerate(messages[:3]):
            print(f"Message {i+1}:")
            print(f"  Line: {msg.get('line_number', 'N/A')}")
            print(f"  Size: {msg['length']} bytes")
            print(f"  Header: {msg['header']}")
            print(f"  Control: 0x{msg.get('control_byte', 0):02x}")
            if 'payload_hex' in msg:
                print(f"  Hex: {binascii.hexlify(msg['raw_data'][:40]).decode()}...")
            print()
    
    def export_to_json(self, messages, filename):
        """Export parsed messages to JSON"""
        import json
        
        # Convert bytes to hex strings for JSON serialization
        json_messages = []
        for msg in messages:
            json_msg = msg.copy()
            if 'raw_data' in json_msg:
                json_msg['raw_data_hex'] = binascii.hexlify(json_msg['raw_data']).decode()
                del json_msg['raw_data']
            if 'payload' in json_msg:
                json_msg['payload_hex'] = binascii.hexlify(json_msg['payload']).decode()
                del json_msg['payload']
            json_messages.append(json_msg)
            
        with open(filename, 'w') as f:
            json.dump(json_messages, f, indent=2)
            
        print(f"💾 Exported {len(messages)} messages to {filename}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 futu_protocol_parser.py <strace_log_file>")
        print("Example: python3 futu_protocol_parser.py futu_analysis/full_traffic.log")
        sys.exit(1)
        
    parser = FutuProtocolParser()
    messages = parser.analyze_strace_file(sys.argv[1])
    
    if messages:
        parser.print_analysis_report(messages)
        
        # Export results
        parser.export_to_json(messages, "parsed_messages.json")
        
        print("\n🎯 Next Steps:")
        print("1. Review the message type breakdown above")
        print("2. Correlate message signatures with GUI actions")
        print("3. Check parsed_messages.json for detailed data")
        print("4. Run enhanced_traffic_capture.sh for more data")
    else:
        print("❌ No Futu protocol messages found in the log file")
        print("💡 Try:")
        print("   1. Ensure FTNN is running and active")
        print("   2. Interact with the FTNN GUI to generate traffic")
        print("   3. Run: ./enhanced_traffic_capture.sh")

if __name__ == "__main__":
    main()
