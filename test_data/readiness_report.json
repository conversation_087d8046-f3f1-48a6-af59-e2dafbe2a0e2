{"timestamp": "2025-06-12T23:57:45.540688", "summary": {"passed": 7, "failed": 4, "warnings": 5, "ready_for_testing": false}, "detailed_results": {"required_python3": {"status": "PASS", "timestamp": "2025-06-12T23:57:45.329804", "details": "Found at /usr/bin/python3"}, "required_tcpdump": {"status": "PASS", "timestamp": "2025-06-12T23:57:45.331139", "details": "Found at /usr/bin/tcpdump"}, "required_pgrep": {"status": "PASS", "timestamp": "2025-06-12T23:57:45.332456", "details": "Found at /usr/bin/pgrep"}, "optional_tshark": {"status": "WARN", "timestamp": "2025-06-12T23:57:45.437230", "details": "Not found - Wireshark command-line tool (preferred for analysis)"}, "optional_netstat": {"status": "WARN", "timestamp": "2025-06-12T23:57:45.531975", "details": "Not found - Network statistics tool"}, "optional_ss": {"status": "PASS", "timestamp": "2025-06-12T23:57:45.533556", "details": "Found at /usr/bin/ss"}, "optional_lsof": {"status": "PASS", "timestamp": "2025-06-12T23:57:45.535673", "details": "Found at /usr/bin/lsof"}, "ftnn_installation": {"status": "PASS", "timestamp": "2025-06-12T23:57:45.535806", "details": "Found at /opt/FTNN/FTNN"}, "ftnn_executable": {"status": "PASS", "timestamp": "2025-06-12T23:57:45.535873", "details": "FTNN is executable"}, "ftnn_running": {"status": "FAIL", "timestamp": "2025-06-12T23:57:45.539384", "details": "FTNN is not running"}, "parser_file_futu_protocol_parser.py": {"status": "FAIL", "timestamp": "2025-06-12T23:57:45.539507", "details": "File missing"}, "parser_file_analyze_payloads.py": {"status": "FAIL", "timestamp": "2025-06-12T23:57:45.539550", "details": "File missing"}, "parser_file_session_replay.py": {"status": "FAIL", "timestamp": "2025-06-12T23:57:45.539594", "details": "File missing"}, "existing_pcap": {"status": "WARN", "timestamp": "2025-06-12T23:57:45.540073", "details": "No existing pcap files found"}, "existing_logs": {"status": "WARN", "timestamp": "2025-06-12T23:57:45.540235", "details": "No existing log files found"}, "existing_json": {"status": "WARN", "timestamp": "2025-06-12T23:57:45.540364", "details": "No existing JSON files found"}}}