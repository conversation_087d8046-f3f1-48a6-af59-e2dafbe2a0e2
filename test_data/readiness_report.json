{"timestamp": "2025-06-12T23:45:31.404682", "summary": {"passed": 13, "failed": 1, "warnings": 4, "ready_for_testing": true}, "detailed_results": {"required_python3": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.181221", "details": "Found at /usr/bin/python3"}, "required_tcpdump": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.182472", "details": "Found at /usr/bin/tcpdump"}, "required_pgrep": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.183788", "details": "Found at /usr/bin/pgrep"}, "optional_tshark": {"status": "WARN", "timestamp": "2025-06-12T23:45:31.297686", "details": "Not found - Wireshark command-line tool (preferred for analysis)"}, "optional_netstat": {"status": "WARN", "timestamp": "2025-06-12T23:45:31.397195", "details": "Not found - Network statistics tool"}, "optional_ss": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.398762", "details": "Found at /usr/bin/ss"}, "optional_lsof": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.400254", "details": "Found at /usr/bin/lsof"}, "ftnn_installation": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.400345", "details": "Found at /opt/FTNN/FTNN"}, "ftnn_executable": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.400381", "details": "FTNN is executable"}, "ftnn_running": {"status": "FAIL", "timestamp": "2025-06-12T23:45:31.403217", "details": "FTNN is not running"}, "parser_file_futu_protocol_parser.py": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.403280", "details": "File exists"}, "parser_file_analyze_payloads.py": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.403330", "details": "File exists"}, "parser_file_session_replay.py": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.403365", "details": "File exists"}, "parser_import": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.403779", "details": "Parser imported successfully"}, "analyzer_import": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.403936", "details": "Payload analyzer imported successfully"}, "existing_pcap": {"status": "WARN", "timestamp": "2025-06-12T23:45:31.404205", "details": "No existing pcap files found"}, "existing_logs": {"status": "WARN", "timestamp": "2025-06-12T23:45:31.404349", "details": "No existing log files found"}, "existing_json": {"status": "PASS", "timestamp": "2025-06-12T23:45:31.404480", "details": "Found 1 JSON files"}}}