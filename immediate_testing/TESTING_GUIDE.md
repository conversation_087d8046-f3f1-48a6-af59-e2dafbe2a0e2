# 📋 Futu Protocol Testing Guide

## 🎯 **Objective**
Extract real-time quote and option data from Futu protocol without trading capabilities.

## ⚡ **Quick Start (3 Steps)**

### **1. System Check**
```bash
python3 run_system_check.py
```
Verify all tools are available and FTNN is ready.

### **2. Start FTNN & Login**
```bash
/opt/FTNN/FTNN
```
- Login with your Futu credentials
- Ensure you can see live market data
- This establishes the server connection we need

### **3. Run Protocol Tests**
```bash
python3 automated_protocol_tester.py
```
Follow the interactive prompts for each test scenario.

## 📊 **Test Scenarios**

### **Test 1: Login Sequence** (3 minutes)
**Purpose**: Capture authentication protocol  
**Actions**: Start FTNN and login  
**Captures**: Handshake, session establishment  

### **Test 2: Quote Subscription** (5 minutes)
**Purpose**: Map quote request/response  
**Actions**: Click on stock quote (e.g., HK.00001)  
**Captures**: Subscribe message, quote data format  

### **Test 3: Option Chain** (5 minutes)
**Purpose**: Analyze option data structure  
**Actions**: Open option chain, select expiry  
**Captures**: Option request, strike prices, IV data  

### **Test 4: Heartbeat Detection** (2 minutes)
**Purpose**: Identify keep-alive protocol  
**Actions**: Leave FTNN idle  
**Captures**: Periodic heartbeat messages  

## 🔍 **Analysis Process**

### **Automatic Analysis**
Each test automatically:
1. **Captures** network traffic with tcpdump
2. **Parses** Futu protocol messages
3. **Analyzes** payload patterns (tickers, prices, timestamps)
4. **Exports** results to JSON files

### **Manual Review**
After testing, review:
```bash
ls test_results/
cat test_results/login_sequence_results.json
cat test_results/quote_subscription_results.json
```

## 📁 **Output Files**

```
test_results/
├── login_sequence.pcap              # Raw network capture
├── login_sequence_results.json     # Parsed messages
├── quote_subscription.pcap
├── quote_subscription_results.json
├── option_chain.pcap
├── option_chain_results.json
├── heartbeat.pcap
├── heartbeat_results.json
└── system_status.json              # System configuration
```

## 🛡️ **Safety Features**

### **Trading Prevention**
- 🚫 **Blocked Message Types**: All 0x3xxx (trading) messages blocked
- ⚠️ **Unknown Messages**: Require user confirmation
- 📋 **Audit Trail**: All outbound messages logged

### **Read-Only Operation**
- ✅ **Quote Data**: Capture and analyze
- ✅ **Option Data**: Extract chains and pricing
- ❌ **Trading**: Completely blocked
- ❌ **Order Management**: Not accessible

## 🔧 **Troubleshooting**

### **FTNN Issues**
```bash
# Check if FTNN is running
pgrep -f FTNN

# Start with display
DISPLAY=:0 /opt/FTNN/FTNN

# Check permissions
ls -la /opt/FTNN/FTNN
```

### **Network Capture Issues**
```bash
# Run with sudo if permission denied
sudo python3 automated_protocol_tester.py

# Check network connections
ss -tnp | grep FTNN
```

### **No Traffic Captured**
- Ensure FTNN is logged in and showing live data
- Try different stocks (HK vs US markets)
- Verify internet connection
- Check firewall settings

## 📊 **Expected Results**

### **Message Types Identified**
- **0x1001**: Login request
- **0x1002**: Login response  
- **0x2001**: Quote subscription
- **0x2002**: Quote data
- **0x2010**: Option chain request
- **0x2011**: Option data
- **0x1010**: Heartbeat/keep-alive

### **Payload Structures**
- **Ticker Symbols**: ASCII encoding (e.g., "HK.00001")
- **Prices**: IEEE-754 float format
- **Timestamps**: Unix timestamp format
- **Option Strikes**: Float array format

### **Protocol Understanding**
- **Header Format**: `FT'n-X\0` + control + sequence + length + type + payload_len
- **Message Flow**: Request/response patterns
- **Keep-Alive**: Heartbeat interval and format

## 🎯 **Success Criteria**

Testing is complete when you have:
- [ ] **Login protocol** captured and understood
- [ ] **Quote subscription** working with symbol variation
- [ ] **Option chain** data structure mapped
- [ ] **Heartbeat interval** identified
- [ ] **Message replay** successfully tested

## 🚀 **Next Steps**

After successful testing:
1. **Review captured message types** and payload structures
2. **Test message replay** with `session_replay.py`
3. **Build production streaming client** using identified protocols
4. **Implement real-time quote/option data extraction**

---

**Estimated Total Time**: 20-30 minutes  
**Expected Outcome**: Complete protocol understanding for quote/option data extraction
