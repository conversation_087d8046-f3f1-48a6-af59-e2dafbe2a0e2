#!/usr/bin/env python3
"""
Payload Analyzer - Deep analysis of Futu protocol message payloads
Focuses on finding quote and option data patterns
"""

import json
import struct
import re
import binascii
from collections import defaultdict

class PayloadAnalyzer:
    def __init__(self):
        self.patterns = {
            'ascii_symbols': [],
            'float_prices': [],
            'timestamps': [],
            'integers': []
        }
    
    def load_messages(self, json_file):
        """Load parsed messages from JSON"""
        try:
            with open(json_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading {json_file}: {e}")
            return []
    
    def find_ascii_patterns(self, payload_hex):
        """Find ASCII text patterns in payload"""
        try:
            payload_bytes = binascii.unhexlify(payload_hex)
            # Look for potential ticker symbols (2-10 chars, alphanumeric + dots)
            ascii_text = payload_bytes.decode('ascii', errors='ignore')
            
            # Find ticker-like patterns: HK.00001, AAPL, etc.
            ticker_patterns = re.findall(r'[A-Z]{1,4}\.?\d{0,5}|[A-Z]{2,6}', ascii_text)
            
            # Find other readable text (3+ consecutive printable chars)
            readable_text = re.findall(r'[A-Za-z0-9\.\-_]{3,}', ascii_text)
            
            return {
                'tickers': ticker_patterns,
                'readable': readable_text,
                'full_ascii': ascii_text
            }
        except:
            return {'tickers': [], 'readable': [], 'full_ascii': ''}
    
    def find_float_patterns(self, payload_hex):
        """Find IEEE-754 float patterns (potential prices)"""
        try:
            payload_bytes = binascii.unhexlify(payload_hex)
            floats = []
            
            # Scan for 4-byte floats (little-endian)
            for i in range(0, len(payload_bytes) - 3, 1):
                try:
                    float_val = struct.unpack('<f', payload_bytes[i:i+4])[0]
                    # Filter for reasonable price ranges (0.01 to 10000)
                    if 0.01 <= float_val <= 10000.0:
                        floats.append({
                            'offset': i,
                            'value': float_val,
                            'hex': binascii.hexlify(payload_bytes[i:i+4]).decode()
                        })
                except:
                    continue
            
            # Scan for 8-byte doubles
            doubles = []
            for i in range(0, len(payload_bytes) - 7, 1):
                try:
                    double_val = struct.unpack('<d', payload_bytes[i:i+8])[0]
                    if 0.01 <= double_val <= 10000.0:
                        doubles.append({
                            'offset': i,
                            'value': double_val,
                            'hex': binascii.hexlify(payload_bytes[i:i+8]).decode()
                        })
                except:
                    continue
                    
            return {'floats': floats, 'doubles': doubles}
        except:
            return {'floats': [], 'doubles': []}
    
    def find_integer_patterns(self, payload_hex):
        """Find integer patterns (timestamps, IDs, counts)"""
        try:
            payload_bytes = binascii.unhexlify(payload_hex)
            integers = []
            
            # Scan for 4-byte integers
            for i in range(0, len(payload_bytes) - 3, 4):  # Aligned access
                try:
                    int_val = struct.unpack('<I', payload_bytes[i:i+4])[0]
                    integers.append({
                        'offset': i,
                        'value': int_val,
                        'hex': binascii.hexlify(payload_bytes[i:i+4]).decode(),
                        'is_timestamp': self.is_timestamp(int_val)
                    })
                except:
                    continue
                    
            return integers
        except:
            return []
    
    def is_timestamp(self, value):
        """Check if integer could be a Unix timestamp"""
        # Unix timestamps for 2020-2030 range
        return 1577836800 <= value <= 1893456000  # 2020-01-01 to 2030-01-01
    
    def analyze_message_group(self, messages, msg_type):
        """Analyze all messages of a specific type"""
        print(f"\n🔍 Analyzing msg_type 0x{msg_type:08x} ({len(messages)} messages)")
        
        if not messages:
            return
        
        # Analyze payloads
        all_ascii = []
        all_floats = []
        all_integers = []
        
        for msg in messages[:5]:  # Analyze first 5 messages
            if 'payload_hex' not in msg or not msg['payload_hex']:
                continue
                
            ascii_data = self.find_ascii_patterns(msg['payload_hex'])
            float_data = self.find_float_patterns(msg['payload_hex'])
            int_data = self.find_integer_patterns(msg['payload_hex'])
            
            all_ascii.extend(ascii_data['tickers'])
            all_floats.extend(float_data['floats'])
            all_integers.extend(int_data)
        
        # Report findings
        if all_ascii:
            unique_tickers = list(set(all_ascii))
            print(f"  📈 Potential tickers: {unique_tickers}")
        
        if all_floats:
            price_values = [f['value'] for f in all_floats[:10]]
            print(f"  💰 Potential prices: {price_values}")
        
        timestamps = [i for i in all_integers if i['is_timestamp']]
        if timestamps:
            print(f"  ⏰ Timestamps found: {len(timestamps)}")
        
        # Show payload structure for first message
        if messages and 'payload_hex' in messages[0]:
            self.show_payload_structure(messages[0]['payload_hex'])
    
    def show_payload_structure(self, payload_hex):
        """Show hex dump with annotations"""
        try:
            payload_bytes = binascii.unhexlify(payload_hex)
            print(f"  📋 Payload structure ({len(payload_bytes)} bytes):")
            
            # Show first 64 bytes in hex dump format
            for i in range(0, min(64, len(payload_bytes)), 16):
                hex_part = ' '.join(f'{b:02x}' for b in payload_bytes[i:i+16])
                ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in payload_bytes[i:i+16])
                print(f"    {i:04x}: {hex_part:<48} |{ascii_part}|")
            
            if len(payload_bytes) > 64:
                print(f"    ... ({len(payload_bytes) - 64} more bytes)")
        except:
            print("  ❌ Could not parse payload structure")
    
    def compare_payloads(self, messages):
        """Compare payloads to find common patterns"""
        if len(messages) < 2:
            return
        
        print(f"\n🔄 Comparing {len(messages)} payloads for patterns...")
        
        # Find common byte sequences
        payload_bytes = []
        for msg in messages[:10]:  # Compare first 10
            if 'payload_hex' in msg and msg['payload_hex']:
                try:
                    payload_bytes.append(binascii.unhexlify(msg['payload_hex']))
                except:
                    continue
        
        if len(payload_bytes) < 2:
            return
        
        # Find common prefixes
        min_len = min(len(p) for p in payload_bytes)
        common_prefix_len = 0
        
        for i in range(min_len):
            if all(p[i] == payload_bytes[0][i] for p in payload_bytes):
                common_prefix_len = i + 1
            else:
                break
        
        if common_prefix_len > 0:
            common_prefix = payload_bytes[0][:common_prefix_len]
            print(f"  🔗 Common prefix: {binascii.hexlify(common_prefix).decode()} ({common_prefix_len} bytes)")
    
    def analyze_all(self, json_file):
        """Main analysis function"""
        print("🔬 FUTU PAYLOAD ANALYSIS")
        print("=" * 50)
        
        messages = self.load_messages(json_file)
        if not messages:
            return
        
        # Group by message type
        by_type = defaultdict(list)
        for msg in messages:
            msg_type = msg.get('msg_type', 0)
            by_type[msg_type].append(msg)
        
        print(f"📊 Found {len(messages)} messages across {len(by_type)} message types")
        
        # Analyze each message type
        for msg_type, msgs in sorted(by_type.items()):
            self.analyze_message_group(msgs, msg_type)
            self.compare_payloads(msgs)
        
        # Summary recommendations
        print("\n🎯 ANALYSIS SUMMARY")
        print("-" * 30)
        print("Next steps based on findings:")
        print("1. Focus on message types with ticker symbols")
        print("2. Map float patterns to price fields")
        print("3. Correlate timestamps with market hours")
        print("4. Test payload modification for symbol changes")

def main():
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python3 analyze_payloads.py parsed_messages.json")
        sys.exit(1)
    
    analyzer = PayloadAnalyzer()
    analyzer.analyze_all(sys.argv[1])

if __name__ == "__main__":
    main()
