#!/usr/bin/env python3
"""
Session Replay Client - Replay captured Futu protocol messages
SAFETY: Only sends read-only quote/option requests, blocks trading messages
"""

import socket
import struct
import time
import json
import binascii
import sys
from datetime import datetime

class FutuReplayClient:
    def __init__(self, server_host, server_port):
        self.host = server_host
        self.port = server_port
        self.socket = None
        self.sequence_id = 1
        self.connected = False
        
        # Safety: Block dangerous message types (trading-related)
        self.blocked_msg_types = set(range(0x3000, 0x4000))  # Block 0x3xxx trading messages
        
        # Allow only quote/option message types (to be discovered)
        self.allowed_msg_types = {
            0x1001,  # Likely login
            0x1002,  # Likely auth response
            0x2001,  # Likely quote subscribe
            0x2002,  # Likely quote data
            0x2010,  # Likely option request
            0x2011,  # Likely option data
            0x1010,  # Likely heartbeat
        }
    
    def connect(self):
        """Connect to Futu server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10.0)
            self.socket.connect((self.host, self.port))
            self.connected = True
            print(f"✅ Connected to {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from server"""
        if self.socket:
            self.socket.close()
            self.connected = False
            print("🔌 Disconnected")
    
    def send_message(self, msg_type, payload=b''):
        """Send a Futu protocol message"""
        if not self.connected:
            print("❌ Not connected")
            return False
        
        # Safety check: Block dangerous message types
        if msg_type in self.blocked_msg_types:
            print(f"🚫 BLOCKED dangerous message type 0x{msg_type:08x}")
            return False
        
        # Warn about unknown message types
        if msg_type not in self.allowed_msg_types:
            response = input(f"⚠️  Unknown message type 0x{msg_type:08x}. Send anyway? (y/N): ")
            if response.lower() != 'y':
                print("❌ Message blocked by user")
                return False
        
        try:
            # Build Futu protocol message
            header = b"FT'n-X\0"
            control_byte = 0x0D  # Common control byte from captures
            total_length = 24 + len(payload)
            payload_length = len(payload)
            
            message = struct.pack('<7sBIIII',
                header,
                control_byte,
                self.sequence_id,
                total_length,
                msg_type,
                payload_length
            ) + payload
            
            self.socket.sendall(message)
            self.sequence_id += 1
            
            print(f"📤 Sent: msg_type=0x{msg_type:08x}, seq={self.sequence_id-1}, payload={len(payload)} bytes")
            return True
            
        except Exception as e:
            print(f"❌ Send failed: {e}")
            return False
    
    def receive_message(self, timeout=5.0):
        """Receive and parse a Futu protocol message"""
        if not self.connected:
            return None
        
        try:
            self.socket.settimeout(timeout)
            
            # Read header first
            header_data = self.socket.recv(24)
            if len(header_data) < 24:
                print("❌ Incomplete header received")
                return None
            
            # Parse header
            if not header_data.startswith(b"FT'n-X"):
                print("❌ Invalid message header")
                return None
            
            control_byte = header_data[7]
            sequence_id, total_length, msg_type, payload_length = struct.unpack('<IIII', header_data[8:24])
            
            # Read payload if present
            payload = b''
            if payload_length > 0:
                payload = self.socket.recv(payload_length)
                if len(payload) < payload_length:
                    print(f"❌ Incomplete payload: got {len(payload)}, expected {payload_length}")
                    return None
            
            message = {
                'timestamp': datetime.now().isoformat(),
                'control_byte': control_byte,
                'sequence_id': sequence_id,
                'total_length': total_length,
                'msg_type': msg_type,
                'payload_length': payload_length,
                'payload': payload,
                'payload_hex': binascii.hexlify(payload).decode() if payload else ''
            }
            
            print(f"📥 Received: msg_type=0x{msg_type:08x}, seq={sequence_id}, payload={len(payload)} bytes")
            return message
            
        except socket.timeout:
            print("⏰ Receive timeout")
            return None
        except Exception as e:
            print(f"❌ Receive failed: {e}")
            return None
    
    def replay_from_json(self, json_file, filter_msg_types=None):
        """Replay messages from captured JSON file"""
        try:
            with open(json_file, 'r') as f:
                messages = json.load(f)
        except Exception as e:
            print(f"❌ Error loading {json_file}: {e}")
            return
        
        print(f"📂 Loaded {len(messages)} messages from {json_file}")
        
        # Filter messages if requested
        if filter_msg_types:
            messages = [msg for msg in messages if msg.get('msg_type') in filter_msg_types]
            print(f"🔍 Filtered to {len(messages)} messages")
        
        # Replay each message
        for i, msg in enumerate(messages):
            msg_type = msg.get('msg_type', 0)
            payload_hex = msg.get('payload_hex', '')
            
            # Convert hex payload back to bytes
            payload = b''
            if payload_hex:
                try:
                    payload = binascii.unhexlify(payload_hex)
                except:
                    print(f"❌ Invalid payload hex in message {i}")
                    continue
            
            print(f"\n📋 Replaying message {i+1}/{len(messages)}")
            
            # Send message
            if self.send_message(msg_type, payload):
                # Wait for response
                response = self.receive_message()
                if response:
                    print(f"✅ Got response: msg_type=0x{response['msg_type']:08x}")
                else:
                    print("⚠️  No response received")
            
            # Small delay between messages
            time.sleep(0.5)
    
    def test_quote_subscription(self, symbol_payload_hex):
        """Test quote subscription with custom symbol"""
        print(f"\n🧪 Testing quote subscription...")
        
        try:
            payload = binascii.unhexlify(symbol_payload_hex)
            
            # Send quote subscription (assuming msg_type 0x2001)
            if self.send_message(0x2001, payload):
                print("⏳ Waiting for quote data...")
                
                # Listen for responses for 10 seconds
                start_time = time.time()
                while time.time() - start_time < 10:
                    response = self.receive_message(timeout=2.0)
                    if response:
                        msg_type = response['msg_type']
                        if msg_type == 0x2002:  # Assuming quote data response
                            print("🎉 Quote data received!")
                            return True
                    else:
                        break
                
                print("⚠️  No quote data received")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
        
        return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Replay Futu protocol messages')
    parser.add_argument('--server', required=True, help='Server IP address')
    parser.add_argument('--port', type=int, required=True, help='Server port')
    parser.add_argument('--json', help='JSON file with captured messages')
    parser.add_argument('--filter-types', help='Comma-separated list of msg_types to replay (hex)')
    parser.add_argument('--test-quote', help='Test quote subscription with payload hex')
    
    args = parser.parse_args()
    
    # Create client
    client = FutuReplayClient(args.server, args.port)
    
    try:
        # Connect to server
        if not client.connect():
            sys.exit(1)
        
        if args.json:
            # Parse filter types if provided
            filter_types = None
            if args.filter_types:
                try:
                    filter_types = [int(t, 16) for t in args.filter_types.split(',')]
                    print(f"🔍 Filtering for message types: {[hex(t) for t in filter_types]}")
                except:
                    print("❌ Invalid filter types format")
                    sys.exit(1)
            
            # Replay from JSON
            client.replay_from_json(args.json, filter_types)
        
        elif args.test_quote:
            # Test quote subscription
            client.test_quote_subscription(args.test_quote)
        
        else:
            print("❌ No action specified. Use --json or --test-quote")
    
    finally:
        client.disconnect()

if __name__ == "__main__":
    main()
