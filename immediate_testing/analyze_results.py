#!/usr/bin/env python3
"""
Results Analyzer - Comprehensive analysis of captured Futu protocol data
"""

import json
import os
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime

class ResultsAnalyzer:
    def __init__(self):
        self.results_dir = Path("test_results")
        self.results_dir.mkdir(exist_ok=True)
        
    def load_test_results(self):
        """Load all test result files"""
        results = {}
        
        for json_file in self.results_dir.glob("*_results.json"):
            test_name = json_file.stem.replace('_results', '')
            try:
                with open(json_file, 'r') as f:
                    results[test_name] = json.load(f)
                print(f"✅ Loaded {test_name}: {len(results[test_name].get('messages', []))} messages")
            except Exception as e:
                print(f"❌ Failed to load {json_file}: {e}")
        
        return results
    
    def analyze_message_types(self, results):
        """Analyze message types across all tests"""
        print("\n🔍 MESSAGE TYPE ANALYSIS")
        print("=" * 50)
        
        all_msg_types = Counter()
        test_msg_types = defaultdict(Counter)
        
        for test_name, test_data in results.items():
            messages = test_data.get('messages', [])
            for msg in messages:
                msg_type = msg.get('msg_type', 0)
                all_msg_types[msg_type] += 1
                test_msg_types[test_name][msg_type] += 1
        
        # Overall message type summary
        print(f"📊 OVERALL MESSAGE TYPES:")
        for msg_type, count in all_msg_types.most_common():
            print(f"   0x{msg_type:08x}: {count} messages")
        
        # Per-test breakdown
        print(f"\n📋 PER-TEST BREAKDOWN:")
        for test_name, msg_counter in test_msg_types.items():
            print(f"\n   {test_name.upper()}:")
            for msg_type, count in msg_counter.most_common():
                print(f"      0x{msg_type:08x}: {count} messages")
        
        return all_msg_types, test_msg_types
    
    def analyze_payload_patterns(self, results):
        """Analyze payload patterns for data extraction"""
        print("\n🔬 PAYLOAD PATTERN ANALYSIS")
        print("=" * 50)
        
        from analyze_payloads import PayloadAnalyzer
        analyzer = PayloadAnalyzer()
        
        pattern_summary = {
            'tickers_found': set(),
            'price_ranges': [],
            'timestamps_found': [],
            'common_patterns': defaultdict(int)
        }
        
        for test_name, test_data in results.items():
            print(f"\n📋 {test_name.upper()}:")
            messages = test_data.get('messages', [])
            
            for msg in messages:
                payload_hex = msg.get('payload_hex', '')
                if not payload_hex:
                    continue
                
                # Analyze patterns
                ascii_patterns = analyzer.find_ascii_patterns(payload_hex)
                float_patterns = analyzer.find_float_patterns(payload_hex)
                int_patterns = analyzer.find_integer_patterns(payload_hex)
                
                # Collect tickers
                for ticker in ascii_patterns['tickers']:
                    pattern_summary['tickers_found'].add(ticker)
                
                # Collect prices
                for float_data in float_patterns['floats']:
                    if 0.01 <= float_data['value'] <= 10000:
                        pattern_summary['price_ranges'].append(float_data['value'])
                
                # Collect timestamps
                for int_data in int_patterns:
                    if int_data['is_timestamp']:
                        pattern_summary['timestamps_found'].append(int_data['value'])
            
            # Show test-specific findings
            test_tickers = set()
            test_prices = []
            
            for msg in messages:
                payload_hex = msg.get('payload_hex', '')
                if payload_hex:
                    ascii_patterns = analyzer.find_ascii_patterns(payload_hex)
                    float_patterns = analyzer.find_float_patterns(payload_hex)
                    
                    test_tickers.update(ascii_patterns['tickers'])
                    test_prices.extend([f['value'] for f in float_patterns['floats'] if 0.01 <= f['value'] <= 10000])
            
            if test_tickers:
                print(f"   📈 Tickers: {sorted(test_tickers)}")
            if test_prices:
                print(f"   💰 Price range: ${min(test_prices):.2f} - ${max(test_prices):.2f}")
        
        # Overall summary
        print(f"\n📊 OVERALL PATTERNS:")
        print(f"   📈 Unique tickers: {len(pattern_summary['tickers_found'])}")
        if pattern_summary['tickers_found']:
            print(f"      {sorted(pattern_summary['tickers_found'])}")
        
        if pattern_summary['price_ranges']:
            print(f"   💰 Price range: ${min(pattern_summary['price_ranges']):.2f} - ${max(pattern_summary['price_ranges']):.2f}")
        
        if pattern_summary['timestamps_found']:
            print(f"   ⏰ Timestamps: {len(pattern_summary['timestamps_found'])} found")
        
        return pattern_summary
    
    def analyze_protocol_structure(self, results):
        """Analyze protocol structure and message flow"""
        print("\n🏗️ PROTOCOL STRUCTURE ANALYSIS")
        print("=" * 50)
        
        sequence_patterns = defaultdict(list)
        message_sizes = defaultdict(list)
        
        for test_name, test_data in results.items():
            messages = test_data.get('messages', [])
            
            print(f"\n📋 {test_name.upper()}:")
            
            for msg in messages:
                msg_type = msg.get('msg_type', 0)
                sequence_id = msg.get('sequence_id', 0)
                total_length = msg.get('total_length', 0)
                payload_length = msg.get('payload_length', 0)
                
                sequence_patterns[test_name].append(sequence_id)
                message_sizes[msg_type].append(total_length)
                
                print(f"   Seq {sequence_id:5d}: Type 0x{msg_type:04x}, Size {total_length:4d}, Payload {payload_length:4d}")
        
        # Analyze sequence patterns
        print(f"\n📊 SEQUENCE ANALYSIS:")
        for test_name, sequences in sequence_patterns.items():
            if len(sequences) > 1:
                increments = [sequences[i+1] - sequences[i] for i in range(len(sequences)-1)]
                avg_increment = sum(increments) / len(increments) if increments else 0
                print(f"   {test_name}: Avg sequence increment: {avg_increment:.1f}")
        
        # Analyze message sizes
        print(f"\n📏 MESSAGE SIZE ANALYSIS:")
        for msg_type, sizes in message_sizes.items():
            if sizes:
                avg_size = sum(sizes) / len(sizes)
                print(f"   0x{msg_type:04x}: Avg {avg_size:.1f} bytes (range: {min(sizes)}-{max(sizes)})")
    
    def generate_protocol_map(self, results, msg_types):
        """Generate a protocol mapping guide"""
        print("\n🗺️ PROTOCOL MAPPING")
        print("=" * 50)
        
        protocol_map = {}
        
        # Map message types to likely functions
        type_mappings = {
            0x1001: "Login Request",
            0x1002: "Login Response", 
            0x1010: "Heartbeat/Keep-Alive",
            0x2001: "Quote Subscription",
            0x2002: "Quote Data",
            0x2010: "Option Chain Request",
            0x2011: "Option Data",
            0x2020: "Market Data Request",
            0x2021: "Market Data Response"
        }
        
        print("📋 IDENTIFIED MESSAGE TYPES:")
        for msg_type, count in msg_types.most_common():
            description = type_mappings.get(msg_type, "Unknown")
            print(f"   0x{msg_type:08x}: {description} ({count} messages)")
            protocol_map[f"0x{msg_type:08x}"] = {
                'description': description,
                'count': count,
                'likely_function': self._guess_function(msg_type, count)
            }
        
        return protocol_map
    
    def _guess_function(self, msg_type, count):
        """Guess message function based on type and frequency"""
        if msg_type in [0x1001, 0x1002]:
            return "Authentication"
        elif msg_type == 0x1010 and count > 5:
            return "Heartbeat (periodic)"
        elif msg_type in [0x2001, 0x2010]:
            return "Data Request"
        elif msg_type in [0x2002, 0x2011]:
            return "Data Response"
        elif 0x3000 <= msg_type <= 0x3FFF:
            return "Trading (BLOCKED)"
        else:
            return "Unknown"
    
    def generate_comprehensive_report(self, results):
        """Generate comprehensive analysis report"""
        print("\n📊 COMPREHENSIVE ANALYSIS REPORT")
        print("=" * 60)
        
        # Load and analyze all data
        msg_types, test_msg_types = self.analyze_message_types(results)
        patterns = self.analyze_payload_patterns(results)
        self.analyze_protocol_structure(results)
        protocol_map = self.generate_protocol_map(results, msg_types)
        
        # Generate summary report
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'tests_analyzed': list(results.keys()),
            'total_messages': sum(msg_types.values()),
            'unique_message_types': len(msg_types),
            'protocol_map': protocol_map,
            'patterns_found': {
                'tickers': list(patterns['tickers_found']),
                'price_count': len(patterns['price_ranges']),
                'timestamp_count': len(patterns['timestamps_found'])
            },
            'recommendations': self._generate_recommendations(msg_types, patterns)
        }
        
        # Save report
        report_file = self.results_dir / "comprehensive_analysis.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📋 ANALYSIS COMPLETE")
        print(f"   📊 {report['total_messages']} total messages analyzed")
        print(f"   🏷️ {report['unique_message_types']} unique message types")
        print(f"   📈 {len(patterns['tickers_found'])} unique tickers found")
        print(f"   📋 Report saved to: {report_file}")
        
        return report
    
    def _generate_recommendations(self, msg_types, patterns):
        """Generate recommendations for next steps"""
        recommendations = []
        
        if any(t in msg_types for t in [0x2001, 0x2002]):
            recommendations.append("Quote protocol identified - ready for quote streaming")
        
        if any(t in msg_types for t in [0x2010, 0x2011]):
            recommendations.append("Option protocol identified - ready for option chain extraction")
        
        if 0x1010 in msg_types and msg_types[0x1010] > 3:
            recommendations.append("Heartbeat protocol identified - implement keep-alive")
        
        if patterns['tickers_found']:
            recommendations.append("Ticker encoding understood - can request specific symbols")
        
        if patterns['price_ranges']:
            recommendations.append("Price format identified - can extract real-time prices")
        
        if len(msg_types) >= 4:
            recommendations.append("Sufficient protocol coverage - ready for replay testing")
        
        return recommendations

def main():
    """Main analysis function"""
    print("🔬 FUTU PROTOCOL RESULTS ANALYZER")
    print("=" * 60)
    
    analyzer = ResultsAnalyzer()
    
    # Load test results
    results = analyzer.load_test_results()
    
    if not results:
        print("❌ No test results found!")
        print("💡 Run automated_protocol_tester.py first to generate test data")
        return
    
    # Generate comprehensive analysis
    report = analyzer.generate_comprehensive_report(results)
    
    # Show recommendations
    print(f"\n🎯 RECOMMENDATIONS:")
    for i, rec in enumerate(report['recommendations'], 1):
        print(f"   {i}. {rec}")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"   1. Review protocol_map in comprehensive_analysis.json")
    print(f"   2. Test message replay with session_replay.py")
    print(f"   3. Build production streaming client")
    print(f"   4. Implement real-time quote/option extraction")

if __name__ == "__main__":
    main()
